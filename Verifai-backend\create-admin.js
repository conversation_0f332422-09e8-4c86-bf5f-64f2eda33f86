const { Client } = require('pg');
const bcrypt = require('bcrypt');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

const saltRounds = 10;

async function createAdminAccount() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        // Check if admin account already exists
        const existingAdmin = await client.query('SELECT username FROM auth WHERE username = $1', ['admin']);
        
        if (existingAdmin.rows.length > 0) {
            console.log('❌ Admin account already exists');
            return;
        }

        // Create admin account
        const username = 'admin';
        const password = 'admin123'; // Simple password for testing
        const role = 'admin';

        console.log('Creating admin account...');
        console.log(`Username: ${username}`);
        console.log(`Password: ${password}`);
        console.log(`Role: ${role}`);

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Try to insert with first_login column, fall back if column doesn't exist
        try {
            await client.query('INSERT INTO auth (username, password, role, first_login) VALUES ($1, $2, $3, $4)', 
                [username, hashedPassword, role, true]);
            console.log('✅ Admin account created successfully with first_login = true');
        } catch (error) {
            if (error.message.includes('first_login')) {
                // If first_login column doesn't exist, use basic insert
                console.log('first_login column not found, using basic insert');
                await client.query('INSERT INTO auth (username, password, role) VALUES ($1, $2, $3)', 
                    [username, hashedPassword, role]);
                console.log('✅ Admin account created successfully (without first_login)');
            } else {
                throw error;
            }
        }

        // Create admin profile
        try {
            await client.query(
                'INSERT INTO profile (username, name, description, website, location, image, role) VALUES ($1, $2, $3, $4, $5, $6, $7)',
                [username, 'System Administrator', 'Default admin account', '', 'System', '', role]
            );
            console.log('✅ Admin profile created successfully');
        } catch (error) {
            console.log('⚠️ Could not create admin profile:', error.message);
        }

        // Verify the account was created
        const verification = await client.query('SELECT username, role FROM auth WHERE username = $1', [username]);
        if (verification.rows.length > 0) {
            console.log('\n🎉 Admin account verification:');
            console.log(`  Username: ${verification.rows[0].username}`);
            console.log(`  Role: ${verification.rows[0].role}`);
        }

        console.log('\n📝 You can now login with:');
        console.log(`  Username: ${username}`);
        console.log(`  Password: ${password}`);

    } catch (error) {
        console.error('❌ Error creating admin account:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Make sure PostgreSQL is running and the connection details are correct');
        } else if (error.code === '42P01') {
            console.log('\n💡 The auth table does not exist. Please create the database tables first');
        }
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the script
createAdminAccount();
