import { Box, CircularProgress, Typography, alpha } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(4),
  position: 'relative',
}));

const LoadingRing = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: 80,
  height: 80,
  marginBottom: theme.spacing(2),
  '&::before': {
    content: '""',
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    borderRadius: '50%',
    background: `conic-gradient(from 0deg, ${alpha(theme.palette.primary.main, 0.1)}, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.main, 0.1)})`,
    animation: `${pulse} 2s ease-in-out infinite`,
    zIndex: -1,
  },
}));

const LoadingSpinner = ({ 
  size = 60, 
  message = 'Loading...', 
  submessage = null,
  variant = 'default' 
}) => {
  return (
    <LoadingContainer>
      <LoadingRing>
        <CircularProgress 
          size={size} 
          thickness={4}
          sx={{
            color: 'primary.main',
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
            },
          }}
        />
      </LoadingRing>
      
      <Typography 
        variant="h6" 
        fontWeight={600} 
        color="primary" 
        sx={{ mb: 1, textAlign: 'center' }}
      >
        {message}
      </Typography>
      
      {submessage && (
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ textAlign: 'center', maxWidth: 300 }}
        >
          {submessage}
        </Typography>
      )}
    </LoadingContainer>
  );
};

export default LoadingSpinner;
