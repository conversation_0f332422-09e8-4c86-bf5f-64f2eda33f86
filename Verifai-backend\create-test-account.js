const { Client } = require('pg');
const bcrypt = require('bcrypt');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

const saltRounds = 10;

async function createTestAccount() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        // Create test manufacturer account
        const username = 'testmanu';
        const password = 'testpass123'; // Test password
        const role = 'manufacturer';

        console.log('Creating test manufacturer account...');
        console.log(`Username: ${username}`);
        console.log(`Password: ${password}`);
        console.log(`Role: ${role}`);

        // Check if account already exists
        const existingAccount = await client.query('SELECT username FROM auth WHERE username = $1', [username]);
        
        if (existingAccount.rows.length > 0) {
            console.log('❌ Test account already exists, deleting and recreating...');
            await client.query('DELETE FROM auth WHERE username = $1', [username]);
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        console.log(`Generated hash: ${hashedPassword}`);
        
        // Test the hash immediately
        const testResult = await bcrypt.compare(password, hashedPassword);
        console.log(`Hash test result: ${testResult}`);

        // Insert the test account with first_login = true
        await client.query(
            'INSERT INTO auth (username, password, role, first_login) VALUES ($1, $2, $3, $4)',
            [username, hashedPassword, role, true]
        );

        console.log('✅ Test account created successfully');

        // Create test profile
        try {
            await client.query(
                'INSERT INTO profile (username, name, description, website, location, image, role) VALUES ($1, $2, $3, $4, $5, $6, $7)',
                [username, 'Test Manufacturer', 'Test manufacturer account for testing first login', '', 'Test Location', '', role]
            );
            console.log('✅ Test profile created successfully');
        } catch (error) {
            console.log('⚠️ Could not create test profile:', error.message);
        }

        // Verify the account was created
        const verification = await client.query('SELECT username, role, first_login FROM auth WHERE username = $1', [username]);
        if (verification.rows.length > 0) {
            const account = verification.rows[0];
            console.log('\n🎉 Test account verification:');
            console.log(`  Username: ${account.username}`);
            console.log(`  Role: ${account.role}`);
            console.log(`  First Login: ${account.first_login}`);
        }

        console.log('\n📝 You can now test with:');
        console.log(`  Username: ${username}`);
        console.log(`  Password: ${password}`);
        console.log('  Expected: Should redirect to password change page');

    } catch (error) {
        console.error('❌ Error creating test account:', error.message);
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the script
createTestAccount();
