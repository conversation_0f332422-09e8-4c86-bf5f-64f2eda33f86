{"name": "verifai-frontend", "version": "0.1.0", "private": true, "dependencies": {"@blackbox-vision/react-qr-reader": "^5.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@mui/icons-material": "^6.4.6", "@mui/lab": "^6.0.0-beta.29", "@mui/material": "^6.4.6", "@mui/system": "^6.4.6", "@mui/x-data-grid": "^7.27.2", "@mui/x-date-pickers": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "apexcharts": "^4.5.0", "axios": "^1.8.3", "dayjs": "^1.11.13", "ethers": "^6.13.5", "html5-qrcode": "^2.3.8", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-geocode": "^1.0.0-alpha.1", "react-qr-code": "2.0.15", "react-qr-reader": "^3.0.0-beta-1", "react-qr-scanner": "^1.0.0-alpha.11", "react-router-dom": "^7.2.0", "react-scripts": "^5.0.1", "react-to-print": "^3.0.5", "recharts": "^2.15.1", "styled-components": "^6.1.15", "web-vitals": "^4.2.4", "web3": "^4.16.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "vite": "vite"}, "eslintConfig": {"extends": []}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/axios": "^0.9.36", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "^4.9.5", "vite": "^6.2.0"}, "proxy": "http://localhost:3000"}