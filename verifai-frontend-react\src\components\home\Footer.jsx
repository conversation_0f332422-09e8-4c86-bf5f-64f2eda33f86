import {
  styled,
  Typography,
  Box,
  Container,
  Grid,
  Divider,
  useTheme,
  alpha,
  <PERSON><PERSON><PERSON><PERSON>on,
  Stack
} from "@mui/material";
import {
  GitHub,
  LinkedIn,
  Twitter,
  Email,
  Security,
  QrCodeScanner
} from "@mui/icons-material";
import { keyframes } from "@mui/material/styles";
import { Link } from "react-router-dom";

// Subtle footer animations
const gentleFloat = keyframes`
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-8px);
    opacity: 0.7;
  }
`;

const subtleGlow = keyframes`
  0%, 100% {
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
  }
`;

const Footer = () => {
  const theme = useTheme();

  const FooterContainer = styled(Box)(({ theme }) => ({
    background: `linear-gradient(135deg,
      ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#f8fafc'} 0%,
      ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#e2e8f0'} 100%
    )`,
    borderTop: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `
        radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.05)} 0%, transparent 50%)
      `,
      pointerEvents: 'none',
    },
  }));

  const FooterLink = styled(Link)(({ theme }) => ({
    color: theme.palette.text.secondary,
    textDecoration: 'none',
    fontSize: '0.9rem',
    fontWeight: 500,
    transition: 'all 0.3s ease',
    display: 'block',
    marginBottom: '8px',
    '&:hover': {
      color: theme.palette.primary.main,
      transform: 'translateX(4px)',
    },
  }));

  const SocialIcon = styled(IconButton)(({ theme }) => ({
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    color: theme.palette.primary.main,
    transition: 'all 0.3s ease',
    '&:hover': {
      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
      color: 'white',
      transform: 'translateY(-2px)',
      boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`,
    },
  }));

  // Footer Particle Component
  const FooterParticle = styled(Box)(({ theme, size = 3, delay = 0, duration = 20 }) => ({
    position: 'absolute',
    width: size,
    height: size,
    borderRadius: '50%',
    background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.4)} 0%, transparent 70%)`,
    animation: `${gentleFloat} ${duration}s ease-in-out infinite, ${subtleGlow} ${duration * 1.5}s ease-in-out infinite`,
    animationDelay: `${delay}s`,
    pointerEvents: 'none',
  }));

  return (
    <FooterContainer>
      {/* Subtle Footer Particles */}
      {Array.from({ length: 6 }).map((_, i) => (
        <FooterParticle
          key={`footer-particle-${i}`}
          size={Math.random() * 4 + 2}
          delay={Math.random() * 10}
          duration={Math.random() * 15 + 20}
          sx={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Box sx={{ py: 8 }}>
          <Grid container spacing={6}>
            {/* Brand Section */}
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Security sx={{ fontSize: 32, color: theme.palette.primary.main, mr: 1 }} />
                  <Typography variant="h5" sx={{ fontWeight: 800, color: theme.palette.primary.main }}>
                    Verifai
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6, mb: 3 }}>
                  Revolutionary blockchain-powered product authentication platform protecting consumers
                  and businesses from counterfeit products.
                </Typography>
                <Stack direction="row" spacing={1}>
                  <SocialIcon size="small">
                    <GitHub fontSize="small" />
                  </SocialIcon>
                  <SocialIcon size="small">
                    <LinkedIn fontSize="small" />
                  </SocialIcon>
                  <SocialIcon size="small">
                    <Twitter fontSize="small" />
                  </SocialIcon>
                  <SocialIcon size="small">
                    <Email fontSize="small" />
                  </SocialIcon>
                </Stack>
              </Box>
            </Grid>

            {/* Quick Links */}
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: theme.palette.text.primary }}>
                Platform
              </Typography>
              <FooterLink to="/scanner">QR Scanner</FooterLink>
              <FooterLink to="/dashboard">Dashboard</FooterLink>
              <FooterLink to="/login">Login</FooterLink>
            </Grid>

            {/* Resources */}
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: theme.palette.text.primary }}>
                Features
              </Typography>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Blockchain Security
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Supply Chain Tracking
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Anti-Counterfeiting
                </Typography>
              </Box>
            </Grid>

            {/* Contact */}
            <Grid item xs={12} md={4}>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: theme.palette.text.primary }}>
                Academic Project
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6, mb: 2 }}>
                This is a final year project demonstrating blockchain-based product authentication technology.
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                Built with React, Material-UI, and Ethereum blockchain technology.
              </Typography>
            </Grid>
          </Grid>

          <Divider sx={{ my: 4, borderColor: alpha(theme.palette.divider, 0.2) }} />

          {/* Bottom Section */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: 2
          }}>
            <Typography variant="body2" color="text.secondary">
              © 2025 Verifai. Final Year Project - All rights reserved.
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <QrCodeScanner sx={{ fontSize: 16, color: theme.palette.primary.main }} />
              <Typography variant="body2" color="text.secondary">
                Powered by Blockchain Technology
              </Typography>
            </Box>
          </Box>
        </Box>
      </Container>
    </FooterContainer>
  );
};

export default Footer;
