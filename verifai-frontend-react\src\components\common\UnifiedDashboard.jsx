import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    Container,
    Typography,
    Grid,
    Stack,
    Avatar,
    Chip,
    Fade,
    Alert,
    useTheme,
    alpha,
    Button,
    Card,
    CardContent,
    IconButton,
    Tooltip
} from '@mui/material';
import ElegantBackground from './ElegantBackground';
import {
    Security,
    Factory,
    LocalShipping,
    Store,
    People,
    Inventory,
    QrCode,
    QrCodeScanner,
    TrendingUp,
    Analytics,
    AccountCircle,
    AddCircleOutline,
    ManageAccounts,
    PersonAdd,
    AccountBalanceWallet,
    CheckCircle,
    Warning,
    ShoppingCart,
    PointOfSale,
    Receipt
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';

// Animations
const pulse = keyframes`
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
`;

const float = keyframes`
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
`;

const shimmer = keyframes`
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
`;



// Styled Components
const FuturisticContainer = styled(Box)(({ theme }) => ({
    minHeight: '100vh',
    background: `
        linear-gradient(135deg,
            #ffffff 0%,
            #fafafa 25%,
            #f5f5f5 50%,
            #fafafa 75%,
            #ffffff 100%
        ),
        radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.02)} 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.02)} 0%, transparent 50%)
    `,
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
            radial-gradient(circle at 50% 50%, ${alpha(theme.palette.primary.main, 0.01)} 0%, transparent 70%),
            linear-gradient(45deg, transparent 30%, ${alpha(theme.palette.secondary.main, 0.01)} 50%, transparent 70%)
        `,
        pointerEvents: 'none',
    }
}));



const StatsCard = styled(Card)(({ theme }) => ({
    background: `
        linear-gradient(135deg,
            ${alpha(theme.palette.background.paper, 0.1)} 0%,
            ${alpha(theme.palette.background.paper, 0.05)} 100%
        )
    `,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: '20px',
    padding: theme.spacing(3),
    height: '100%',
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
        transform: 'translateY(-8px) scale(1.02)',
        boxShadow: `
            0 20px 60px ${alpha(theme.palette.primary.main, 0.15)},
            0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)}
        `,
        background: `
            linear-gradient(135deg,
                ${alpha(theme.palette.background.paper, 0.15)} 0%,
                ${alpha(theme.palette.background.paper, 0.08)} 100%
            )
        `,
    },
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '-100%',
        width: '100%',
        height: '100%',
        background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.primary.main, 0.1)}, transparent)`,
        transition: 'left 0.5s',
    },
    '&:hover::before': {
        left: '100%',
    }
}));

const TechCard = styled(Card)(({ theme }) => ({
    background: `
        linear-gradient(135deg,
            ${alpha(theme.palette.background.paper, 0.1)} 0%,
            ${alpha(theme.palette.background.paper, 0.05)} 100%
        )
    `,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: '24px',
    height: '100%',
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
        transform: 'translateY(-12px) scale(1.02)',
        boxShadow: `
            0 25px 80px ${alpha(theme.palette.primary.main, 0.2)},
            0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)}
        `,
        background: `
            linear-gradient(135deg,
                ${alpha(theme.palette.background.paper, 0.15)} 0%,
                ${alpha(theme.palette.background.paper, 0.08)} 100%
            )
        `,
    },
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
            linear-gradient(135deg, 
                ${alpha(theme.palette.primary.main, 0.05)} 0%, 
                transparent 50%,
                ${alpha(theme.palette.secondary.main, 0.05)} 100%
            )
        `,
        opacity: 0,
        transition: 'opacity 0.3s ease',
    },
    '&:hover::before': {
        opacity: 1,
    }
}));

const WalletCard = styled(Card)(({ theme }) => ({
    background: `
        linear-gradient(135deg,
            ${alpha('#4ecdc4', 0.1)} 0%,
            ${alpha('#44a08d', 0.05)} 100%
        )
    `,
    backdropFilter: 'blur(20px)',
    border: `2px solid ${alpha('#4ecdc4', 0.2)}`,
    borderRadius: '20px',
    padding: theme.spacing(3),
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: `0 15px 40px ${alpha('#4ecdc4', 0.2)}`,
        border: `2px solid ${alpha('#4ecdc4', 0.4)}`,
    }
}));

// Dashboard Configuration
export const DASHBOARD_CONFIGS = {
    admin: {
        title: 'Admin Dashboard',
        subtitle: 'System Administration & Management',
        icon: <Security fontSize="large" />,
        chipLabel: 'Super Admin',
        chipColor: 'primary',
        primaryColor: '#1976d2',
        secondaryColor: '#1565c0',
        stats: [
            { icon: <People />, label: 'Total Users', value: '1,234', change: '+12%' },
            { icon: <Inventory />, label: 'Products Verified', value: '45,678', change: '+8%' },
            { icon: <Analytics />, label: 'Success Rate', value: '99.2%', change: '+0.3%' },
            { icon: <TrendingUp />, label: 'Monthly Growth', value: '23%', change: '+5%' },
        ],
        quickActions: [
            {
                title: 'User Management',
                description: 'Manage user accounts, roles, and permissions across the platform',
                icon: <ManageAccounts />,
                color: 'primary',
                path: '/manage-account',
                buttonText: 'Manage Users'
            },
            {
                title: 'Add New Account',
                description: 'Create new user accounts for manufacturers, suppliers, and retailers',
                icon: <PersonAdd />,
                color: 'secondary',
                path: '/add-account',
                buttonText: 'Add Account'
            }
        ]
    },
    manufacturer: {
        title: 'Manufacturer Dashboard',
        subtitle: 'Blockchain-Powered Product Management',
        icon: <Factory fontSize="large" />,
        chipLabel: 'Manufacturing Portal',
        chipColor: 'primary',
        primaryColor: '#1976d2',
        secondaryColor: '#9c27b0',
        stats: [
            { icon: <Inventory />, label: 'Products Created', value: '24', change: '+12%' },
            { icon: <QrCode />, label: 'QR Codes Generated', value: '156', change: '+8%' },
            { icon: <Security />, label: 'Blockchain Verified', value: '100%', change: '0%' },
            { icon: <TrendingUp />, label: 'Success Rate', value: '98.5%', change: '+2%' },
        ],
        quickActions: [
            {
                title: 'Manufacturer Profile',
                description: 'View and manage your manufacturer profile details and credentials',
                icon: <AccountCircle />,
                color: 'primary',
                path: '/profile',
                buttonText: 'View Profile'
            },
            {
                title: 'Add New Product',
                description: 'Register new products to the blockchain network with secure verification',
                icon: <AddCircleOutline />,
                color: 'secondary',
                path: '/add-product',
                buttonText: 'Add Product',
                requiresWallet: true
            }
        ]
    },
    supplier: {
        title: 'Supplier Dashboard',
        subtitle: 'Blockchain-Powered Supply Management',
        icon: <LocalShipping fontSize="large" />,
        chipLabel: 'Supply Chain Portal',
        chipColor: 'success',
        primaryColor: '#2e7d32',
        secondaryColor: '#388e3c',
        stats: [
            { icon: <LocalShipping />, label: 'Products Supplied', value: '89', change: '+15%' },
            { icon: <QrCodeScanner />, label: 'QR Codes Scanned', value: '234', change: '+12%' },
            { icon: <Security />, label: 'Verified Shipments', value: '100%', change: '0%' },
            { icon: <TrendingUp />, label: 'Supply Rate', value: '96.8%', change: '+3%' },
        ],
        quickActions: [
            {
                title: 'Supplier Profile',
                description: 'View and manage your supplier profile details and logistics information',
                icon: <AccountCircle />,
                color: 'success',
                path: '/profile',
                buttonText: 'View Profile'
            },
            {
                title: 'Scan & Update Products',
                description: 'Scan QR codes to update product status and track supply chain movements',
                icon: <QrCodeScanner />,
                color: 'primary',
                path: '/scanner',
                buttonText: 'Start Scanning',
                requiresWallet: true
            }
        ]
    },
    retailer: {
        title: 'Retailer Dashboard',
        subtitle: 'Blockchain-Powered Retail Management',
        icon: <Store fontSize="large" />,
        chipLabel: 'Retail Portal',
        chipColor: 'warning',
        primaryColor: '#ff6b35',
        secondaryColor: '#f7931e',
        stats: [
            { icon: <ShoppingCart />, label: 'Products Sold', value: '156', change: '+18%' },
            { icon: <QrCodeScanner />, label: 'QR Codes Scanned', value: '342', change: '+22%' },
            { icon: <Security />, label: 'Verified Sales', value: '100%', change: '0%' },
            { icon: <TrendingUp />, label: 'Sales Growth', value: '94.2%', change: '+5%' },
        ],
        quickActions: [
            {
                title: 'Retailer Profile',
                description: 'View and manage your retailer profile details and store information',
                icon: <AccountCircle />,
                color: 'warning',
                path: '/profile',
                buttonText: 'View Profile'
            },
            {
                title: 'Scan & Update Products',
                description: 'Scan QR codes to update product status and track retail transactions',
                icon: <QrCodeScanner />,
                color: 'primary',
                path: '/scanner',
                buttonText: 'Start Scanning',
                requiresWallet: true
            }
        ]
    }
};

// Utility functions
const getEthereumObject = () => window.ethereum;

const findMetaMaskAccount = async () => {
    try {
        const ethereum = getEthereumObject();
        if (!ethereum) {
            console.error("Make sure you have Metamask!");
            return null;
        }

        const accounts = await ethereum.request({ method: "eth_accounts" });
        if (accounts.length !== 0) {
            const account = accounts[0];
            console.log("Found an authorized account:", account);
            return account;
        } else {
            console.error("No authorized account found");
            return null;
        }
    } catch (error) {
        console.error(error);
        return null;
    }
};

// Main Unified Dashboard Component
const UnifiedDashboard = ({ userRole }) => {
    const theme = useTheme();
    const navigate = useNavigate();
    const [currentAccount, setCurrentAccount] = useState("");
    const [isConnecting, setIsConnecting] = useState(false);

    const config = DASHBOARD_CONFIGS[userRole] || DASHBOARD_CONFIGS.admin;

    useEffect(() => {
        findMetaMaskAccount().then((account) => {
            if (account !== null) {
                setCurrentAccount(account);
            }
        });
    }, []);

    const connectWallet = async () => {
        try {
            setIsConnecting(true);
            const ethereum = getEthereumObject();
            if (!ethereum) {
                alert("Please install MetaMask to connect your wallet!");
                return;
            }

            const accounts = await ethereum.request({
                method: "eth_requestAccounts",
            });

            console.log("Connected", accounts[0]);
            setCurrentAccount(accounts[0]);
        } catch (error) {
            console.error("Failed to connect wallet:", error);
        } finally {
            setIsConnecting(false);
        }
    };

    return (
        <FuturisticContainer>
            {/* Elegant Animated Background */}
            <ElegantBackground variant="dashboard" intensity="medium" />

            <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 2 }}>
                {/* Header Section */}
                <Fade in={true} timeout={800}>
                    <Box sx={{
                        mb: 6,
                        p: 4,
                        background: `
                            linear-gradient(135deg,
                                ${alpha(theme.palette.background.paper, 0.1)} 0%,
                                ${alpha(theme.palette.background.paper, 0.05)} 100%
                            )
                        `,
                        backdropFilter: 'blur(20px)',
                        borderRadius: '24px',
                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        boxShadow: `
                            0 20px 60px ${alpha(theme.palette.common.black, 0.1)},
                            inset 0 1px 0 ${alpha(theme.palette.common.white, 0.1)}
                        `,
                    }}>
                        <Stack direction="row" alignItems="center" spacing={3}>
                            <Avatar
                                sx={{
                                    width: 80,
                                    height: 80,
                                    background: `linear-gradient(135deg, ${config.primaryColor} 0%, ${config.secondaryColor} 100%)`,
                                    boxShadow: `
                                        0 15px 35px ${alpha(config.primaryColor, 0.4)},
                                        inset 0 2px 0 ${alpha(theme.palette.common.white, 0.3)}
                                    `,
                                    animation: `${pulse} 3s ease-in-out infinite`,
                                }}
                            >
                                {config.icon}
                            </Avatar>
                            <Box>
                                <Typography variant="h3" component="h1" sx={{ fontWeight: 800, mb: 1 }}>
                                    {config.title}
                                </Typography>
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Chip
                                        label={config.chipLabel}
                                        color={config.chipColor}
                                        size="small"
                                        sx={{ fontWeight: 600 }}
                                    />
                                    <Typography variant="body2" color="text.secondary">
                                        {config.subtitle}
                                    </Typography>
                                </Stack>
                            </Box>
                        </Stack>
                    </Box>
                </Fade>

                {/* Wallet Connection Status (for roles that need it) */}
                {(userRole === 'manufacturer' || userRole === 'supplier' || userRole === 'retailer') && (
                    <Fade in={true} timeout={1000}>
                        <Box sx={{ mb: 4 }}>
                            <WalletCard>
                                <Stack direction="row" alignItems="center" spacing={3}>
                                    <Avatar
                                        sx={{
                                            bgcolor: currentAccount ? '#4ecdc4' : '#ff6b35',
                                            color: 'white',
                                            width: 56,
                                            height: 56,
                                            boxShadow: `0 8px 25px ${alpha(currentAccount ? '#4ecdc4' : '#ff6b35', 0.3)}`,
                                        }}
                                    >
                                        {currentAccount ? <CheckCircle /> : <Warning />}
                                    </Avatar>
                                    <Box sx={{ flex: 1 }}>
                                        <Typography variant="h6" sx={{ fontWeight: 700, mb: 1 }}>
                                            {currentAccount ? '🔗 Wallet Connected' : '⚠️ Wallet Not Connected'}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {currentAccount
                                                ? `Connected: ${currentAccount.slice(0, 6)}...${currentAccount.slice(-4)}`
                                                : 'Connect your MetaMask wallet to access blockchain features'
                                            }
                                        </Typography>
                                    </Box>
                                    {!currentAccount && (
                                        <Button
                                            variant="contained"
                                            onClick={connectWallet}
                                            disabled={isConnecting}
                                            startIcon={<AccountBalanceWallet />}
                                            sx={{
                                                background: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)',
                                                borderRadius: '12px',
                                                px: 3,
                                                py: 1.5,
                                                fontWeight: 600,
                                                textTransform: 'none',
                                                boxShadow: `0 8px 25px ${alpha('#4ecdc4', 0.3)}`,
                                                '&:hover': {
                                                    background: 'linear-gradient(135deg, #44a08d 0%, #4ecdc4 100%)',
                                                    transform: 'translateY(-2px)',
                                                    boxShadow: `0 12px 35px ${alpha('#4ecdc4', 0.4)}`,
                                                }
                                            }}
                                        >
                                            {isConnecting ? 'Connecting...' : 'Connect Wallet'}
                                        </Button>
                                    )}
                                </Stack>
                            </WalletCard>
                        </Box>
                    </Fade>
                )}

                {/* Statistics Grid */}
                <Fade in={true} timeout={1200}>
                    <Grid container spacing={3} sx={{ mb: 6 }}>
                        {config.stats.map((stat, index) => (
                            <Grid item xs={12} sm={6} md={3} key={index}>
                                <StatsCard>
                                    <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                                        <Avatar
                                            sx={{
                                                bgcolor: (() => {
                                                    const colors = [config.primaryColor, theme.palette.primary.main, theme.palette.success.main, '#4ecdc4'];
                                                    return colors[index] || config.primaryColor;
                                                })(),
                                                color: '#ffffff',
                                                width: 56,
                                                height: 56,
                                                boxShadow: `0 8px 25px ${alpha(
                                                    (() => {
                                                        const colors = [config.primaryColor, theme.palette.primary.main, theme.palette.success.main, '#4ecdc4'];
                                                        return colors[index] || config.primaryColor;
                                                    })(), 0.3
                                                )}`,
                                                background: `linear-gradient(135deg, ${(() => {
                                                    const colors = [config.primaryColor, theme.palette.primary.main, theme.palette.success.main, '#4ecdc4'];
                                                    return colors[index] || config.primaryColor;
                                                })()} 0%, ${(() => {
                                                    const colors = [config.secondaryColor, theme.palette.primary.dark, theme.palette.success.dark, '#44a08d'];
                                                    return colors[index] || config.secondaryColor;
                                                })()} 100%)`,
                                            }}
                                        >
                                            {stat.icon}
                                        </Avatar>
                                        <Box sx={{ flex: 1 }}>
                                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                {stat.label}
                                            </Typography>
                                            <Stack direction="row" alignItems="center" spacing={1}>
                                                <Typography variant="h5" sx={{ fontWeight: 800, color: 'text.primary' }}>
                                                    {stat.value}
                                                </Typography>
                                                <Chip
                                                    label={stat.change}
                                                    size="small"
                                                    color={stat.change.startsWith('+') ? 'success' : 'error'}
                                                    sx={{
                                                        fontWeight: 600,
                                                        fontSize: '0.75rem',
                                                        height: 24,
                                                    }}
                                                />
                                            </Stack>
                                        </Box>
                                    </Stack>
                                </StatsCard>
                            </Grid>
                        ))}
                    </Grid>
                </Fade>

                {/* Quick Actions */}
                <Fade in={true} timeout={1400}>
                    <Grid container spacing={4} sx={{ mb: 6 }}>
                        {config.quickActions.map((action, index) => (
                            <Grid item xs={12} md={6} key={index}>
                                <TechCard>
                                    <Box sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                                        <Stack direction="row" alignItems="center" spacing={3} sx={{ mb: 3 }}>
                                            <Avatar
                                                sx={{
                                                    bgcolor: (() => {
                                                        const colorMap = {
                                                            'primary': theme.palette.primary.main,
                                                            'secondary': theme.palette.secondary.main,
                                                            'success': theme.palette.success.main,
                                                            'warning': config.primaryColor,
                                                        };
                                                        return colorMap[action.color] || theme.palette.primary.main;
                                                    })(),
                                                    color: '#ffffff',
                                                    width: 72,
                                                    height: 72,
                                                    boxShadow: `0 12px 35px ${alpha((() => {
                                                        const colorMap = {
                                                            'primary': theme.palette.primary.main,
                                                            'secondary': theme.palette.secondary.main,
                                                            'success': theme.palette.success.main,
                                                            'warning': config.primaryColor,
                                                        };
                                                        return colorMap[action.color] || theme.palette.primary.main;
                                                    })(), 0.4)}`,
                                                    background: `linear-gradient(135deg, ${(() => {
                                                        const colorMap = {
                                                            'primary': theme.palette.primary.main,
                                                            'secondary': theme.palette.secondary.main,
                                                            'success': theme.palette.success.main,
                                                            'warning': config.primaryColor,
                                                        };
                                                        return colorMap[action.color] || theme.palette.primary.main;
                                                    })()} 0%, ${(() => {
                                                        const colorMap = {
                                                            'primary': theme.palette.primary.dark,
                                                            'secondary': theme.palette.secondary.dark,
                                                            'success': theme.palette.success.dark,
                                                            'warning': config.secondaryColor,
                                                        };
                                                        return colorMap[action.color] || theme.palette.primary.dark;
                                                    })()} 100%)`,
                                                    animation: `${float} 3s ease-in-out infinite`,
                                                }}
                                            >
                                                {action.icon}
                                            </Avatar>
                                            <Box sx={{ flex: 1 }}>
                                                <Typography variant="h5" sx={{ fontWeight: 800, mb: 1, color: 'text.primary' }}>
                                                    {action.title}
                                                </Typography>
                                                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                                                    {action.description}
                                                </Typography>
                                            </Box>
                                        </Stack>

                                        <Box sx={{ mt: 'auto' }}>
                                            {action.requiresWallet && !currentAccount ? (
                                                <Alert
                                                    severity="warning"
                                                    sx={{
                                                        mb: 2,
                                                        borderRadius: '12px',
                                                        background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)} 0%, ${alpha(theme.palette.warning.main, 0.05)} 100%)`,
                                                        border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                                                    }}
                                                >
                                                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                                        🔗 Wallet connection required for this feature
                                                    </Typography>
                                                </Alert>
                                            ) : null}

                                            <Button
                                                variant="contained"
                                                fullWidth
                                                onClick={() => navigate(action.path)}
                                                disabled={action.requiresWallet && !currentAccount}
                                                sx={{
                                                    py: 2,
                                                    borderRadius: '16px',
                                                    background: (() => {
                                                        const colorMap = {
                                                            'primary': `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                                            'secondary': `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,
                                                            'success': `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                                            'warning': `linear-gradient(135deg, ${config.primaryColor} 0%, ${config.secondaryColor} 100%)`,
                                                        };
                                                        return colorMap[action.color] || `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`;
                                                    })(),
                                                    fontWeight: 700,
                                                    fontSize: '1rem',
                                                    textTransform: 'none',
                                                    boxShadow: `0 8px 25px ${alpha((() => {
                                                        const colorMap = {
                                                            'primary': theme.palette.primary.main,
                                                            'secondary': theme.palette.secondary.main,
                                                            'success': theme.palette.success.main,
                                                            'warning': config.primaryColor,
                                                        };
                                                        return colorMap[action.color] || theme.palette.primary.main;
                                                    })(), 0.3)}`,
                                                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                    '&:hover': {
                                                        transform: 'translateY(-4px) scale(1.02)',
                                                        boxShadow: `0 15px 40px ${alpha((() => {
                                                            const colorMap = {
                                                                'primary': theme.palette.primary.main,
                                                                'secondary': theme.palette.secondary.main,
                                                                'success': theme.palette.success.main,
                                                                'warning': config.primaryColor,
                                                            };
                                                            return colorMap[action.color] || theme.palette.primary.main;
                                                        })(), 0.4)}`,
                                                        background: (() => {
                                                            const colorMap = {
                                                                'primary': `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                                                                'secondary': `linear-gradient(135deg, ${theme.palette.secondary.light} 0%, ${theme.palette.secondary.main} 100%)`,
                                                                'success': `linear-gradient(135deg, ${theme.palette.success.light} 0%, ${theme.palette.success.main} 100%)`,
                                                                'warning': `linear-gradient(135deg, ${config.secondaryColor} 0%, ${config.primaryColor} 100%)`,
                                                            };
                                                            return colorMap[action.color] || `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`;
                                                        })(),
                                                    },
                                                    '&:disabled': {
                                                        background: `linear-gradient(135deg, ${alpha(theme.palette.action.disabled, 0.3)} 0%, ${alpha(theme.palette.action.disabled, 0.1)} 100%)`,
                                                        color: theme.palette.action.disabled,
                                                        boxShadow: 'none',
                                                    }
                                                }}
                                            >
                                                {action.buttonText}
                                            </Button>
                                        </Box>
                                    </Box>
                                </TechCard>
                            </Grid>
                        ))}
                    </Grid>
                </Fade>

                {/* Role-specific Additional Features */}
                {userRole === 'retailer' && (
                    <Fade in={true} timeout={1600}>
                        <Box sx={{
                            p: 4,
                            background: `
                                linear-gradient(135deg,
                                    ${alpha(theme.palette.background.paper, 0.1)} 0%,
                                    ${alpha(theme.palette.background.paper, 0.05)} 100%
                                )
                            `,
                            backdropFilter: 'blur(20px)',
                            borderRadius: '24px',
                            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                            boxShadow: `
                                0 20px 60px ${alpha(theme.palette.common.black, 0.1)},
                                inset 0 1px 0 ${alpha(theme.palette.common.white, 0.1)}
                            `,
                        }}>
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 3, textAlign: 'center' }}>
                                🏪 Retail Management Features
                            </Typography>

                            <Grid container spacing={3}>
                                <Grid item xs={12} md={4}>
                                    <Box sx={{ textAlign: 'center', p: 3 }}>
                                        <Avatar
                                            sx={{
                                                width: 64,
                                                height: 64,
                                                mx: 'auto',
                                                mb: 2,
                                                background: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)',
                                                boxShadow: `0 12px 35px ${alpha('#4ecdc4', 0.3)}`,
                                            }}
                                        >
                                            <PointOfSale fontSize="large" />
                                        </Avatar>
                                        <Typography variant="h6" sx={{ fontWeight: 700, mb: 1 }}>
                                            Point of Sale
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Process retail transactions with blockchain verification
                                        </Typography>
                                    </Box>
                                </Grid>

                                <Grid item xs={12} md={4}>
                                    <Box sx={{ textAlign: 'center', p: 3 }}>
                                        <Avatar
                                            sx={{
                                                width: 64,
                                                height: 64,
                                                mx: 'auto',
                                                mb: 2,
                                                background: `linear-gradient(135deg, ${config.primaryColor} 0%, ${config.secondaryColor} 100%)`,
                                                boxShadow: `0 12px 35px ${alpha(config.primaryColor, 0.3)}`,
                                            }}
                                        >
                                            <Receipt fontSize="large" />
                                        </Avatar>
                                        <Typography variant="h6" sx={{ fontWeight: 700, mb: 1 }}>
                                            Sales Tracking
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Monitor sales performance and product movement
                                        </Typography>
                                    </Box>
                                </Grid>

                                <Grid item xs={12} md={4}>
                                    <Box sx={{ textAlign: 'center', p: 3 }}>
                                        <Avatar
                                            sx={{
                                                width: 64,
                                                height: 64,
                                                mx: 'auto',
                                                mb: 2,
                                                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                                boxShadow: `0 12px 35px ${alpha(theme.palette.primary.main, 0.3)}`,
                                            }}
                                        >
                                            <Analytics fontSize="large" />
                                        </Avatar>
                                        <Typography variant="h6" sx={{ fontWeight: 700, mb: 1 }}>
                                            Analytics
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Access detailed insights and retail analytics
                                        </Typography>
                                    </Box>
                                </Grid>
                            </Grid>
                        </Box>
                    </Fade>
                )}
            </Container>
        </FuturisticContainer>
    );
};

export default UnifiedDashboard;
export { FuturisticContainer, StatsCard, TechCard, WalletCard, pulse, float, shimmer };
