import { 
  Box, 
  Paper, 
  Typography, 
  IconButton, 
  Tooltip, 
  Button,
  Chip,
  Divider,
  Avatar,
  LinearProgress,
  styled
} from '@mui/material';
import bgImg from '../../img/bg.png';
import axios from 'axios';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowBack, 
  Edit, 
  Save, 
  Refresh, 
  ManageAccounts,
  VerifiedUser,
  Group 
} from '@mui/icons-material';
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#3f51b5',
    },
    secondary: {
      main: '#9c27b0',
    },
    background: {
      default: '#f5f7fa'
    }
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
    },
  },
});

const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
  border: 'none',
  [`& .${gridClasses.row}.even`]: {
    backgroundColor: theme.palette.grey[50],
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  [`& .${gridClasses.row}.odd`]: {
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  [`& .${gridClasses.cell}`]: {
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  [`& .${gridClasses.columnHeader}`]: {
    backgroundColor: theme.palette.grey[100],
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
}));

const columns = [
  { 
    field: 'name', 
    headerName: 'Name', 
    width: 180, 
    editable: true,
    renderCell: (params) => (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Avatar 
          src={params.row.image} 
          sx={{ width: 32, height: 32, mr: 2 }}
        />
        {params.value}
      </Box>
    )
  },
  {
    field: 'description',
    headerName: 'Description',
    width: 300,
    editable: true,
    renderCell: (params) => (
      <Typography variant="body2" noWrap>
        {params.value || 'No description'}
      </Typography>
    )
  },
  { 
    field: 'username', 
    headerName: 'Username', 
    width: 150, 
    editable: true
  },
  { 
    field: 'website', 
    headerName: 'Website', 
    width: 200, 
    editable: true,
    renderCell: (params) => (
      params.value ? (
        <a href={params.value.startsWith('http') ? params.value : `https://${params.value}`} 
           target="_blank" 
           rel="noopener noreferrer"
           style={{ color: '#3f51b5', textDecoration: 'none' }}>
          {params.value.replace(/(^\w+:|^)\/\//, '')}
        </a>
      ) : 'Not provided'
    )
  },
  { 
    field: 'location', 
    headerName: 'Location', 
    width: 180, 
    editable: true
  },
  { 
    field: 'role', 
    headerName: 'Role', 
    width: 130, 
    editable: true,
    renderCell: (params) => (
      <Chip 
        label={params.value} 
        size="small" 
        color={
          params.value === 'Admin' ? 'secondary' : 
          params.value === 'Manufacturer' ? 'primary' : 'default'
        }
        sx={{ 
          fontWeight: 600,
          textTransform: 'capitalize'
        }}
      />
    )
  }
];

const ManageAccount = () => {
  const [rows, setRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedCount, setSelectedCount] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    handleData();
  }, []);

  const handleData = async () => {
    setLoading(true);
    try {
      const res = await axios.get('http://localhost:3000/profileAll');
      setRows(res.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleRefresh = () => {
    handleData();
  };

  const handleSelectionChange = (selection) => {
    setSelectedCount(selection.length);
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{
        background: 'linear-gradient(to right, #e0eafc, #cfdef3)',
        minHeight: "100vh",
        padding: '2rem',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <Paper elevation={6} sx={{ 
          width: "90%", 
          maxWidth: "1400px",
          height: "auto",
          minHeight: "600px",
          padding: "2rem",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(31, 38, 135, 0.15)'
        }}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '1.5rem'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ManageAccounts sx={{ 
                fontSize: '2.5rem',
                color: 'primary.main',
                mr: 2 
              }} />
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 700,
                  background: 'linear-gradient(90deg, #3f51b5 0%, #9c27b0 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                User Management
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Refresh data">
                <IconButton 
                  onClick={handleRefresh}
                  sx={{ 
                    mr: 2,
                    backgroundColor: 'rgba(63, 81, 181, 0.08)',
                    '&:hover': {
                      backgroundColor: 'rgba(63, 81, 181, 0.2)'
                    }
                  }}
                >
                  <Refresh color="primary" />
                </IconButton>
              </Tooltip>
              
              <Button
                onClick={handleBack}
                variant="outlined"
                startIcon={<ArrowBack />}
                sx={{
                  textTransform: 'none',
                  borderRadius: '12px',
                  padding: '8px 16px',
                  fontWeight: 600,
                  borderWidth: '2px',
                  '&:hover': {
                    borderWidth: '2px',
                    backgroundColor: 'rgba(63, 81, 181, 0.04)'
                  }
                }}
              >
                Back to Dashboard
              </Button>
            </Box>
          </Box>

          <Paper 
            elevation={0}
            sx={{ 
              height: '500px', 
              width: '100%', 
              borderRadius: '12px',
              overflow: 'hidden',
              border: '1px solid rgba(0, 0, 0, 0.12)'
            }}
          >
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 1.5,
              borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
              backgroundColor: 'rgba(63, 81, 181, 0.05)'
            }}>
              <Group color="primary" sx={{ mr: 1 }} />
              <Typography variant="subtitle1" color="textSecondary">
                {rows.length} user accounts • {selectedCount} selected
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Chip 
                icon={<VerifiedUser />} 
                label="Admin View" 
                color="secondary" 
                size="small" 
                sx={{ fontWeight: 600 }} 
              />
            </Box>

            {loading && <LinearProgress color="primary" />}

            <StyledDataGrid
              rows={rows}
              columns={columns}
              pageSize={5}
              rowsPerPageOptions={[5, 10, 25]}
              loading={loading}
              checkboxSelection
              disableSelectionOnClick
              onSelectionModelChange={handleSelectionChange}
              getRowClassName={(params) => 
                params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
              }
              sx={{
                '& .MuiDataGrid-cell:focus': {
                  outline: 'none'
                },
                '& .MuiDataGrid-columnHeader:focus': {
                  outline: 'none'
                }
              }}
            />
          </Paper>
          
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: '1.5rem'
          }}>
            <Typography variant="body2" color="textSecondary">
              Last updated: {new Date().toLocaleString()}
            </Typography>

            <Box>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<Edit />}
                sx={{
                  textTransform: 'none',
                  borderRadius: '12px',
                  padding: '8px 16px',
                  fontWeight: 600,
                  mr: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(63, 81, 181, 0.04)'
                  }
                }}
              >
                Edit Selected
              </Button>

              <Button
                variant="contained"
                color="primary"
                startIcon={<Save />}
                sx={{
                  textTransform: 'none',
                  borderRadius: '12px',
                  padding: '8px 24px',
                  fontWeight: 600,
                  boxShadow: '0 2px 12px rgba(63, 81, 181, 0.2)',
                  '&:hover': {
                    boxShadow: '0 4px 16px rgba(63, 81, 181, 0.3)'
                  }
                }}
              >
                Save Changes
              </Button>
            </Box>
          </Box>

          <Divider sx={{ my: 3 }} />

          <Box sx={{ 
            display: 'flex',
            justifyContent: 'center',
          }}>
            <Typography variant="body2" color="text.secondary">
              Admin Portal v1.0 • Secure Access Management
            </Typography>
          </Box>
        </Paper>
      </Box>
    </ThemeProvider>
  );
};

export default ManageAccount;