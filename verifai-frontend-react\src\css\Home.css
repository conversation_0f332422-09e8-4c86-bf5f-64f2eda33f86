.home-container {
    background: url('../img/bg.png') center center/cover no-repeat;
    background-color: rgb(39, 50, 98);
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: inset 0 0 0 1000px rgba(0, 0, 0, 0.2);
    object-fit: contain;
    justify-content: center;
  }
  
  @keyframes move {
    100% {
      transform: translate(41px, 0px);
    }
  }
  
  .home-container > h1 {
    color: #080707;
    margin-top: 50px;
    /* font-size: 100px;
    text-align: center; */
    font-size: 120px;
    text-transform: uppercase;
    font-family: '<PERSON><PERSON><PERSON><PERSON>', serif;
    letter-spacing: -3px;
    transition: 700ms ease;
    font-variation-settings: "wght" 311;
    margin-bottom: 0.8rem;
    outline: none;
    text-align: center;
  
  }
  
  h1:hover {
    font-variation-settings: "wght" 582; 
    letter-spacing: 1px;
  }

  
  .home-container > p {
    margin-top: 15px;
    margin-bottom: 10px;
    color: #fff;
    font-size: 32px;
    font-family: sans-serif;
    line-height: 150%;
    text-align: center;
    color: MintCream;
    letter-spacing: .5px;
  }
  
  .btn {
    margin: 6px;
  }
  