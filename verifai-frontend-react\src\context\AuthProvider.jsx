import { createContext, useState, useEffect, useCallback } from "react";
import authService from '../services/authService';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
    const [auth, setAuth] = useState({
        user: null,
        accessToken: null,
        isAuthenticated: false,
        isLoading: true
    });

    // Initialize auth state from stored data
    const initializeAuth = useCallback(async () => {
        try {
            const storedUser = authService.getUser();
            const storedToken = authService.getAccessToken();

            if (storedUser && storedToken) {
                // Verify token is still valid
                const verification = await authService.verifyToken();

                if (verification.valid) {
                    setAuth({
                        user: verification.user,
                        accessToken: authService.getAccessToken(),
                        isAuthenticated: true,
                        isLoading: false
                    });
                } else {
                    // Token invalid, clear auth
                    authService.clearAuthData();
                    setAuth({
                        user: null,
                        accessToken: null,
                        isAuthenticated: false,
                        isLoading: false
                    });
                }
            } else {
                setAuth({
                    user: null,
                    accessToken: null,
                    isAuthenticated: false,
                    isLoading: false
                });
            }
        } catch (error) {
            console.error('Auth initialization error:', error);
            setAuth({
                user: null,
                accessToken: null,
                isAuthenticated: false,
                isLoading: false
            });
        }
    }, []);

    // Login function
    const login = async (username, password, rememberMe = false) => {
        try {
            const result = await authService.login(username, password, rememberMe);

            if (result.success) {
                setAuth({
                    user: result.user,
                    accessToken: result.accessToken,
                    isAuthenticated: true,
                    isLoading: false
                });

                // Start token refresh timer
                authService.startTokenRefreshTimer();
            }

            return result;
        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                message: 'Login failed. Please try again.'
            };
        }
    };

    // Logout function
    const logout = async () => {
        try {
            await authService.logout();
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            setAuth({
                user: null,
                accessToken: null,
                isAuthenticated: false,
                isLoading: false
            });
        }
    };

    // Update auth state
    const updateAuth = (newAuthData) => {
        setAuth(prevAuth => ({
            ...prevAuth,
            ...newAuthData
        }));
    };

    // Listen for auth state changes
    useEffect(() => {
        const handleAuthStateChange = (event) => {
            if (!event.detail.authenticated) {
                setAuth({
                    user: null,
                    accessToken: null,
                    isAuthenticated: false,
                    isLoading: false
                });
            }
        };

        window.addEventListener('authStateChanged', handleAuthStateChange);

        return () => {
            window.removeEventListener('authStateChanged', handleAuthStateChange);
        };
    }, []);

    // Initialize auth on mount
    useEffect(() => {
        initializeAuth();
    }, [initializeAuth]);

    const contextValue = {
        auth,
        setAuth: updateAuth,
        login,
        logout,
        isAuthenticated: auth.isAuthenticated,
        isLoading: auth.isLoading,
        user: auth.user,
        accessToken: auth.accessToken
    };

    return (
        <AuthContext.Provider value={contextValue}>
            {children}
        </AuthContext.Provider>
    );
};

export default AuthContext;