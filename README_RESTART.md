# 🚀 VERIFAI - Quick Restart Guide

## 📋 **4-Step Restart Process**

### **1. Start Ganache** 
```bash
# Option A: Open Ganache GUI (Recommended)
# Option B: ganache-cli -p 7545 -i 1337
```

### **2. Deploy Contract**
```bash
cd verifai-smartcontract-solidity
node scripts/deploy-ganache.js
```

### **3. Start Backend**
```bash
cd verifai-backend-nodejs
npm start
```

### **4. Start Frontend**
```bash
cd verifai-frontend-react
npm run vite
```

## 🔑 **MetaMask Setup**
- **Network**: Ganache Local
- **RPC URL**: http://127.0.0.1:7545
- **Chain ID**: 1337
- **Import Account**: Copy private key from Ganache

## 🧪 **Test URLs**
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:3000
- **Login**: testmanu/testpass123

## ⚠️ **Quick Fixes**
- **Contract issues**: Re-run deploy script
- **MetaMask 0 ETH**: Re-import Ganache account
- **Connection errors**: Restart all servers

**✅ Success**: Registration completes in 3-8 seconds with QR code generation
