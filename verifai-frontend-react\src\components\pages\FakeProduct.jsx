import { Box, Paper, Typography, Button, Container, Grid } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import WarningIcon from '@mui/icons-material/Warning';
import SecurityIcon from '@mui/icons-material/Security';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const FakeProduct = () => {
    const navigate = useNavigate();

    const handleBack = () => {
        navigate(-2);
    };

    return (
        <Box sx={{
            minHeight: "100vh",
            background: "linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            py: 8,
            px: 2
        }}>
            <Container maxWidth="md">
                <Paper elevation={6} sx={{
                    borderRadius: 3,
                    overflow: "hidden",
                    boxShadow: "0 15px 30px rgba(0,0,0,0.1)",
                    position: "relative"
                }}>
                    {/* Header with warning stripe */}
                    <Box sx={{
                        backgroundColor: "#d32f2f",
                        color: "white",
                        py: 3,
                        px: 4,
                        display: "flex",
                        alignItems: "center",
                        gap: 2
                    }}>
                        <WarningIcon sx={{ fontSize: 40 }} />
                        <Typography variant="h4" sx={{
                            fontFamily: "'Poppins', sans-serif",
                            fontWeight: 700
                        }}>
                            Product Authentication Failed
                        </Typography>
                    </Box>

                    <Box sx={{ p: { xs: 3, md: 5 }, backgroundColor: "white" }}>
                        <Typography variant="h6" sx={{
                            fontFamily: "'Poppins', sans-serif",
                            fontWeight: 600,
                            color: "#d32f2f",
                            mb: 3,
                            textAlign: "center"
                        }}>
                            WARNING: Potential Counterfeit Product Detected
                        </Typography>

                        <Grid container spacing={4}>
                            <Grid item xs={12} md={6}>
                                <Box sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    textAlign: "center",
                                    height: "100%",
                                    p: 2,
                                    borderRight: { md: "1px solid #e0e0e0" }
                                }}>
                                    <SecurityIcon sx={{
                                        fontSize: 60,
                                        color: "#d32f2f",
                                        mb: 2
                                    }} />
                                    <Typography variant="body1" sx={{
                                        fontFamily: "'Roboto', sans-serif",
                                        mb: 2,
                                        color: "#555"
                                    }}>
                                        Our verification system has determined that this product does not match our official records. This item may be counterfeit.
                                    </Typography>
                                </Box>
                            </Grid>

                            <Grid item xs={12} md={6}>
                                <Box sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    textAlign: "center",
                                    height: "100%",
                                    p: 2
                                }}>
                                    <ContactSupportIcon sx={{
                                        fontSize: 60,
                                        color: "#1976d2",
                                        mb: 2
                                    }} />
                                    <Typography variant="body1" sx={{
                                        fontFamily: "'Roboto', sans-serif",
                                        mb: 2,
                                        color: "#555"
                                    }}>
                                        For your safety, we recommend discontinuing use of this product and contacting the manufacturer for verification.
                                    </Typography>
                                </Box>
                            </Grid>
                        </Grid>

                        <Box sx={{
                            backgroundColor: "#fff8e1",
                            borderLeft: "4px solid #ffc107",
                            p: 3,
                            my: 4,
                            borderRadius: 1
                        }}>
                            <Typography variant="body2" sx={{
                                fontFamily: "'Roboto', sans-serif",
                                fontWeight: 500,
                                color: "#5d4037"
                            }}>
                                <strong>Important:</strong> Counterfeit products may not meet safety standards and could pose health risks. Genuine products undergo rigorous quality control and safety testing.
                            </Typography>
                        </Box>

                        <Box sx={{ mt: 4, textAlign: "center" }}>
                            <Button
                                onClick={handleBack}
                                variant="contained"
                                color="primary"
                                startIcon={<ArrowBackIcon />}
                                sx={{
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 2,
                                    textTransform: "none",
                                    fontSize: "1rem",
                                    boxShadow: "0 3px 5px rgba(0,0,0,0.1)",
                                    '&:hover': {
                                        boxShadow: "0 5px 8px rgba(0,0,0,0.15)"
                                    }
                                }}
                            >
                                Return to Scanner
                            </Button>
                        </Box>
                    </Box>

                    {/* Footer */}
                    <Box sx={{
                        backgroundColor: "#f5f5f5",
                        py: 2,
                        px: 4,
                        textAlign: "center",
                        borderTop: "1px solid #e0e0e0"
                    }}>
                        <Typography variant="caption" sx={{
                            fontFamily: "'Roboto', sans-serif",
                            color: "#757575"
                        }}>
                            Thank you for using our Anti-Counterfeit Verification System
                        </Typography>
                    </Box>
                </Paper>
            </Container>
        </Box>
    );
};

export default FakeProduct;