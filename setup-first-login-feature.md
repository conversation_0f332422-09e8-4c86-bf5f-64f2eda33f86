b nirst Login Password Change Feature Setup

This guide will help you set up the "force password change on first login" feature for your Verifai application.

## Overview

This feature ensures that when an admin creates an account for a user, the user must change their password on their first login. This improves security by preventing the reuse of admin-set passwords.

## Database Setup

### Option 1: For New Installations
If you're setting up a fresh database, the `verifai-db/create_table.txt` file has been updated to include the `first_login` column automatically.

### Option 2: For Existing Installations
If you have an existing database, run the migration script:

```sql
-- Connect to your PostgreSQL database and run:
\i verifai-db/migration_add_first_login.sql
```

Or manually execute:
```sql
ALTER TABLE public.auth 
ADD COLUMN IF NOT EXISTS first_login boolean DEFAULT true NOT NULL;

UPDATE public.auth 
SET first_login = false 
WHERE first_login IS NULL;
```

## How It Works

### Backend Changes
1. **Account Creation**: When admin creates accounts via `/addaccount`, `first_login` is set to `true`
2. **Login Flow**: The `/auth` endpoint checks the `first_login` flag
3. **Password Change**: The `/changepsw` endpoint sets `first_login` to `false` after successful password change

### Frontend Changes
1. **Login Component**: Detects first-time login and redirects to password change page
2. **Password Change Page**: Enhanced with futuristic UI and better validation
3. **Authentication Service**: Handles the first login flow seamlessly

## User Experience Flow

1. **Admin creates account** → User receives username/password
2. **User attempts login** → System detects first login
3. **Automatic redirect** → User taken to password change page
4. **Current password verification** → User must enter admin-assigned password
5. **New password creation** → User sets their own secure password
6. **Password confirmation** → User confirms the new password
7. **Redirect to login** → User can now log in normally with new password

## Security Benefits

- ✅ Forces users to change admin-set passwords
- ✅ Prevents password reuse across multiple accounts
- ✅ Requires current password verification before change
- ✅ Prevents unauthorized password changes
- ✅ Improves overall security posture
- ✅ Maintains audit trail of password changes
- ✅ No additional database tables needed

## Testing the Feature

1. **Create a test account** as admin:
   ```
   Username: testuser
   Password: TempPass123!
   Role: manufacturer
   ```

2. **Attempt to login** with the test account
3. **Verify redirect** to password change page
4. **Enter current password** (TempPass123!)
5. **Set new password** and confirm it
6. **Verify password change** completes successfully
7. **Login again** with new password to ensure normal flow works

### Automated Testing

Run the automated test script to verify all functionality:
```bash
cd Verifai-backend
node test-password-change.js
```

This will test:
- ✅ First-time login detection
- ✅ Current password verification
- ✅ Password change with wrong current password (should fail)
- ✅ Password change with correct current password (should succeed)
- ✅ Login with old password (should fail)
- ✅ Login with new password (should succeed)

## Configuration

No additional configuration is required. The feature works out of the box with your existing setup.

## Troubleshooting

### Issue: Migration fails
- **Solution**: Ensure you have proper database permissions
- **Check**: Verify PostgreSQL connection settings

### Issue: Users not redirected to password change
- **Solution**: Clear browser localStorage and try again
- **Check**: Verify backend is returning `requirePasswordChange: true`

### Issue: Password change fails
- **Solution**: Check password meets requirements (8+ chars, uppercase, number, special char)
- **Check**: Verify `/changepsw` endpoint is accessible

## Files Modified

### Backend
- `Verifai-backend/postgres.js` - Updated login and password change logic
- `verifai-db/create_table.txt` - Added first_login column
- `verifai-db/migration_add_first_login.sql` - Migration script

### Frontend
- `verifai-frontend-react/src/services/authService.js` - Handle first login flow
- `verifai-frontend-react/src/components/home/<USER>
- `verifai-frontend-react/src/components/pages/ChangePasswordPrompt.jsx` - Enhanced UI and UX

## Support

If you encounter any issues, check:
1. Database connection and permissions
2. Backend server logs for errors
3. Browser console for frontend errors
4. Network tab for API call responses

The implementation is designed to be minimal and non-disruptive to your existing functionality.
