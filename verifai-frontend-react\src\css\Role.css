.role-container {
    background: url('../img/bg.png') center center/cover no-repeat;
    background-color: rgb(39, 50, 98);
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: inset 0 0 0 1000px rgba(0, 0, 0, 0.2);
    object-fit: contain;
    justify-content: center;
}

.role-container-box {
    width: 400px;
    height: 500px;
    background-color: #e3eefc;
    padding: 20px;
    border-radius: 2%;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.role-container-top {
    margin-top: 10%;
    width: 400px;
    height: 500px;
    background-color: #e3eefc;
    padding: 20px;
    border-radius: 2%;
    position: absolute;
    display: table;
    align-items: center;
    flex-direction: column;
}

.role-container-box>h1 {
    color: #193453;
    top: 4.7em;
    margin-top: 5px;
    font-size: 45px;
    font-family: 'Gambet<PERSON>', serif;
    letter-spacing: -1px;
    transition: 700ms ease;
    font-variation-settings: "wght" 311;
    margin-bottom: 20px;
    outline: none;
    text-align: center;
}

.role-container-box>h2 {
    color: #193453;
    font-family: 'Gambetta', serif;
    font-weight: lighter;
    letter-spacing: 0.5px;
    font-size: 18px;
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
}

.role-container-top>h1 {
    color: #193453;
    top: 4.7em;
    margin-top: 5px;
    font-size: 45px;
    font-family: 'Gambetta', serif;
    letter-spacing: -1px;
    transition: 700ms ease;
    font-variation-settings: "wght" 311;
    margin-bottom: 20px;
    outline: none;
    text-align: center;
}

form.role{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
