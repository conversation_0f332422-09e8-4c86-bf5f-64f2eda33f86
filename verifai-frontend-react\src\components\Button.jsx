import '../css/Button.css';
import { useState, useEffect } from 'react';

const STYLES = ['btn--primary', 'btn--outline', 'btn--long'];
const SIZES = ['btn--medium', 'btn--large'];

export const Button = ({
    children,
    type,
    onClick,
    buttonStyle,
    buttonSize,
    loading = false
}) => {
    const checkButtonStyle = STYLES.includes(buttonStyle)
        ? buttonStyle
        : STYLES[0];

    const checkButtonSize = SIZES.includes(buttonSize)
        ? buttonSize
        : SIZES[0];

    const [startTime, setStartTime] = useState(null);
    
    useEffect(() => {
        setStartTime(Date.now());
    }, []);
    
    return (
            <button
                className={`btn ${checkButtonStyle} ${checkButtonSize} ${loading ? 'loading' : ''}`}
                onClick={onClick}
                type={type}
            >
                {children}
            </button>
    );
}