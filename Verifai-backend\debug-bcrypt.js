const { Client } = require('pg');
const bcrypt = require('bcrypt');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

async function debugBcrypt() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        // Get the stored hash for admin
        const result = await client.query('SELECT password FROM auth WHERE username = $1', ['admin']);
        
        if (result.rows.length === 0) {
            console.log('❌ No admin user found');
            return;
        }

        const storedHash = result.rows[0].password;
        console.log('🔐 Stored hash:', storedHash);
        console.log('🔐 Hash length:', storedHash.length);
        console.log('🔐 Hash starts with:', storedHash.substring(0, 10));

        // Test password
        const testPassword = 'admin123';
        console.log('🔑 Test password:', testPassword);
        console.log('🔑 Password length:', testPassword.length);

        // Test bcrypt comparison
        console.log('\n🧪 Testing bcrypt comparison...');
        const isMatch = await bcrypt.compare(testPassword, storedHash);
        console.log('✅ Bcrypt result:', isMatch);

        // Generate a new hash for comparison
        console.log('\n🔨 Generating new hash for comparison...');
        const newHash = await bcrypt.hash(testPassword, 10);
        console.log('🔐 New hash:', newHash);
        
        const newHashTest = await bcrypt.compare(testPassword, newHash);
        console.log('✅ New hash test:', newHashTest);

        // Test if the stored hash is valid
        console.log('\n🔍 Testing stored hash validity...');
        try {
            const hashInfo = storedHash.split('$');
            console.log('Hash parts:', hashInfo);
            console.log('Algorithm:', hashInfo[1]);
            console.log('Rounds:', hashInfo[2]);
            console.log('Salt+Hash:', hashInfo[3]);
        } catch (error) {
            console.log('❌ Error parsing hash:', error.message);
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the debug
debugBcrypt();
