import { Box, Container, styled, Typography, Grid, useTheme } from "@mui/material";
import React from "react";
import logoImg from "../../img/logo.png";
import starsImg from "../../img/Star.png";
import logosImg from "../../img/logos.png";

const Companies = () => {
  const theme = useTheme();

  const CustomContainer = styled(Container)(({ theme }) => ({
    padding: theme.spacing(6, 0),
    position: 'relative',
    '&:before': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: '50%',
      transform: 'translateX(-50%)',
      width: '80%',
      height: '1px',
      backgroundColor: theme.palette.divider,
    }
  }));

  const PartnerLogo = styled('img')({
    filter: 'grayscale(100%)',
    opacity: 0.7,
    transition: 'all 0.3s ease',
    '&:hover': {
      filter: 'grayscale(0%)',
      opacity: 1,
      transform: 'scale(1.05)'
    }
  });

  return (
    <Box sx={{ 
      backgroundColor: theme.palette.background.paper,
      py: 8,
      position: 'relative',
      overflow: 'hidden',
      '&:after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '4px',
        background: 'linear-gradient(90deg, #3f51b5, #2196f3, #00bcd4)',
      }
    }}>
      <CustomContainer maxWidth="lg">
        {/* Header Section */}
        <Box sx={{ 
          textAlign: 'center',
          mb: 8,
          position: 'relative'
        }}>
          <Typography variant="h4" component="h2" sx={{
            fontWeight: 700,
            mb: 2,
            color: theme.palette.text.primary,
            position: 'relative',
            display: 'inline-block',
            '&:after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: '50%',
              transform: 'translateX(-50%)',
              width: '60%',
              height: '3px',
              background: theme.palette.primary.main,
              borderRadius: '3px'
            }
          }}>
            Trusted by Industry Leaders
          </Typography>
          
          <Typography variant="subtitle1" sx={{
            color: theme.palette.text.secondary,
            maxWidth: '700px',
            mx: 'auto',
            lineHeight: 1.6
          }}>
            Our system powers businesses across multiple industries, delivering exceptional results and ROI.
          </Typography>
          
          <Box sx={{ 
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            mt: 3
          }}>
            <img 
              src={starsImg} 
              alt="5-star rating" 
              style={{ 
                width: '120px',
                marginRight: '16px'
              }} 
            />
            <Typography variant="caption" sx={{
              color: theme.palette.text.secondary,
              fontStyle: 'italic'
            }}>
              Rated 4.9/5 by our customers
            </Typography>
          </Box>
        </Box>
        
        {/* Main Logo */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          mb: 6,
          animation: 'pulse 2s infinite',
          '@keyframes pulse': {
            '0%': { transform: 'scale(1)' },
            '50%': { transform: 'scale(1.05)' },
            '100%': { transform: 'scale(1)' }
          }
        }}>
          <img 
            src={logoImg} 
            alt="Company logo" 
            style={{ 
              maxHeight: '80px',
              width: 'auto'
            }} 
          />
        </Box>
        
        {/* Partner Logos Grid */}
        <Grid container spacing={4} sx={{
          justifyContent: 'center',
          alignItems: 'center',
          mt: 4
        }}>
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <Grid item xs={6} sm={4} md={2} key={item}>
              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '80px',
                p: 2,
                backgroundColor: theme.palette.mode === 'dark' ? 
                  'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                borderRadius: '8px',
                transition: 'all 0.3s ease',
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark' ? 
                    'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                  boxShadow: theme.shadows[2]
                }
              }}>
                <PartnerLogo 
                  src={logosImg} 
                  alt={`Partner ${item}`} 
                  style={{ 
                    maxHeight: '40px',
                    maxWidth: '100%',
                    width: 'auto'
                  }} 
                />
              </Box>
            </Grid>
          ))}
        </Grid>
        
        {/* Footer Note */}
        <Box sx={{ 
          textAlign: 'center',
          mt: 6,
          pt: 4,
          position: 'relative'
        }}>
          <Typography variant="body2" sx={{
            color: theme.palette.text.secondary,
            fontSize: '0.75rem',
            letterSpacing: '0.5px'
          }}>
            An enterprise system by <strong>Global Rigorous Aura Success (M) Sdn Bhd</strong>
          </Typography>
          <Typography variant="caption" sx={{
            display: 'block',
            color: theme.palette.text.disabled,
            mt: 1
          }}>
            © {new Date().getFullYear()} All Rights Reserved
          </Typography>
        </Box>
      </CustomContainer>
    </Box>
  );
};

export default Companies;