import {
  <PERSON>,
  Button,
  styled,
  Typography,
  Container,
  Grid,
  Chip,
  Stack,
  useTheme,
  alpha
} from "@mui/material";
import {
  Qr<PERSON>odeScanner,
  Security,
  Verified,
  TrendingUp,
  ArrowForward
} from "@mui/icons-material";
import { keyframes } from "@mui/material/styles";
import { Link } from "react-router-dom";
import heroImg from "../../img/hero_illustration.png";

// Beautiful particle animations
const floatingParticle = keyframes`
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) translateX(10px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40px) translateX(-5px) scale(1.2);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px) translateX(-10px) scale(1.1);
    opacity: 0.8;
  }
`;

const glowPulse = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    opacity: 0.6;
  }
  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6), 0 0 60px rgba(99, 102, 241, 0.4);
    opacity: 1;
  }
`;

const driftAcross = keyframes`
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  10%, 90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 100px));
    opacity: 0;
  }
`;

// Hero Particle Components
const HeroParticle = styled(Box)(({ theme, size = 6, delay = 0, duration = 15, type = 'float' }) => ({
  position: 'absolute',
  width: size,
  height: size,
  borderRadius: '50%',
  background: type === 'glow'
    ? `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.8)} 0%, ${alpha(theme.palette.primary.main, 0.4)} 50%, transparent 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.7)} 0%, ${alpha(theme.palette.secondary.main, 0.7)} 100%)`,
  animation: type === 'glow'
    ? `${glowPulse} ${duration}s ease-in-out infinite`
    : type === 'drift'
    ? `${driftAcross} ${duration}s linear infinite`
    : `${floatingParticle} ${duration}s cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  animationDelay: `${delay}s`,
  filter: type === 'glow' ? 'blur(1px)' : 'none',
  pointerEvents: 'none',
}));

const HeroContainer = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#ffffff'} 0%,
    ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#f8fafc'} 50%,
    ${theme.palette.mode === 'dark' ? '#16213e' : '#e2e8f0'} 100%
  )`,
  minHeight: '95vh',
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 20%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, ${alpha(theme.palette.info.main, 0.08)} 0%, transparent 50%)
    `,
    pointerEvents: 'none',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        ${alpha(theme.palette.primary.main, 0.02)} 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        ${alpha(theme.palette.secondary.main, 0.02)} 100px
      )
    `,
    pointerEvents: 'none',
  },
}));

const HeroTitle = styled(Typography)(({ theme }) => ({
  fontSize: 'clamp(2.8rem, 6vw, 5rem)',
  fontWeight: 900,
  lineHeight: 1.1,
  marginBottom: theme.spacing(3),
  background: `linear-gradient(135deg,
    ${theme.palette.primary.main} 0%,
    ${theme.palette.secondary.main} 50%,
    ${theme.palette.primary.dark} 100%
  )`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  letterSpacing: '-0.02em',
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
  textShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.3)}`,
}));

const HeroSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.3rem',
  color: theme.palette.text.secondary,
  lineHeight: 1.7,
  marginBottom: theme.spacing(5),
  maxWidth: '650px',
  fontWeight: 500,
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
}));

const FeatureChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.primary.main, 0.1)} 0%,
    ${alpha(theme.palette.secondary.main, 0.1)} 100%
  )`,
  color: theme.palette.primary.main,
  fontWeight: 600,
  borderRadius: '16px',
  padding: theme.spacing(1, 2),
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  transition: 'all 0.3s ease',
  '& .MuiChip-icon': {
    color: theme.palette.primary.main,
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.primary.main, 0.15)} 0%,
      ${alpha(theme.palette.secondary.main, 0.15)} 100%
    )`,
  },
}));

const CTAButton = styled(Button)(({ theme }) => ({
  fontSize: '1.2rem',
  fontWeight: 700,
  padding: theme.spacing(2, 5),
  borderRadius: '20px',
  textTransform: 'none',
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  boxShadow: `
    0 12px 30px ${alpha(theme.palette.primary.main, 0.3)},
    0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)}
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-3px) scale(1.02)',
    boxShadow: `
      0 16px 40px ${alpha(theme.palette.primary.main, 0.4)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)}
    `,
    '&::before': {
      left: '100%',
    },
  },
}));

const StatsBox = styled(Box)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  backdropFilter: 'blur(20px)',
  borderRadius: '20px',
  padding: theme.spacing(3),
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  textAlign: 'center',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

const Hero = () => {
  const theme = useTheme();

  const features = [
    { icon: <Security />, label: 'Blockchain Secured' },
    { icon: <Verified />, label: 'Instant Verification' },
    { icon: <TrendingUp />, label: 'Supply Chain Tracking' },
  ];

  const stats = [
    { number: '10K+', label: 'Products Verified' },
    { number: '500+', label: 'Trusted Partners' },
    { number: '99.9%', label: 'Accuracy Rate' },
  ];

  return (
    <HeroContainer>
      {/* Beautiful Hero Particle Effects */}
      {Array.from({ length: 12 }).map((_, i) => (
        <HeroParticle
          key={`float-${i}`}
          size={Math.random() * 8 + 4}
          delay={Math.random() * 10}
          duration={Math.random() * 10 + 15}
          type="float"
          sx={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
        />
      ))}

      {/* Glowing Particles */}
      {Array.from({ length: 6 }).map((_, i) => (
        <HeroParticle
          key={`glow-${i}`}
          size={Math.random() * 20 + 15}
          delay={Math.random() * 8}
          duration={Math.random() * 8 + 12}
          type="glow"
          sx={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
        />
      ))}

      {/* Drifting Particles */}
      {Array.from({ length: 4 }).map((_, i) => (
        <HeroParticle
          key={`drift-${i}`}
          size={Math.random() * 6 + 3}
          delay={i * 5}
          duration={20 + Math.random() * 10}
          type="drift"
          sx={{
            top: `${20 + Math.random() * 60}%`,
            left: '-100px',
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Grid container spacing={6} alignItems="center">
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 3 }}>
              <Chip
                label="🚀 Next-Gen Product Authentication"
                variant="outlined"
                sx={{
                  mb: 3,
                  borderColor: theme.palette.primary.main,
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                }}
              />
            </Box>

            <HeroTitle variant="h1">
              Securely Authenticate Your Products with Verifai
            </HeroTitle>

            <HeroSubtitle>
              Our blockchain-powered identification platform ensures robust product verification,
              delivering a dependable solution to authenticate items and safeguard against counterfeit activity.
            </HeroSubtitle>

            <Stack direction="row" spacing={2} sx={{ mb: 4, flexWrap: 'wrap', gap: 1 }}>
              {features.map((feature, index) => (
                <FeatureChip
                  key={index}
                  icon={feature.icon}
                  label={feature.label}
                  variant="outlined"
                />
              ))}
            </Stack>

            <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
              <Link to="/scanner" style={{ textDecoration: 'none' }}>
                <CTAButton
                  variant="contained"
                  size="large"
                  endIcon={<QrCodeScanner />}
                >
                  Start Scanning
                </CTAButton>
              </Link>
              <Button
                variant="outlined"
                size="large"
                endIcon={<ArrowForward />}
                sx={{
                  borderRadius: '16px',
                  borderWidth: '2px',
                  fontWeight: 600,
                  '&:hover': {
                    borderWidth: '2px',
                    transform: 'translateY(-1px)',
                  },
                }}
              >
                Learn More
              </Button>
            </Stack>

            <Grid container spacing={3} sx={{ mt: 2 }}>
              {stats.map((stat, index) => (
                <Grid item xs={4} key={index}>
                  <StatsBox>
                    <Typography variant="h4" fontWeight={800} color="primary">
                      {stat.number}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </StatsBox>
                </Grid>
              ))}
            </Grid>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box
              sx={{
                position: 'relative',
                textAlign: 'center',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: '120%',
                  height: '120%',
                  background: `conic-gradient(from 0deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.1)})`,
                  borderRadius: '50%',
                  animation: 'rotate 20s linear infinite',
                  zIndex: -1,
                },
                '@keyframes rotate': {
                  '0%': { transform: 'translate(-50%, -50%) rotate(0deg)' },
                  '100%': { transform: 'translate(-50%, -50%) rotate(360deg)' },
                },
              }}
            >
              <img
                src={heroImg}
                alt="Verifai Hero Illustration"
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  filter: 'drop-shadow(0 20px 40px rgba(0, 0, 0, 0.1))',
                  borderRadius: '20px',
                }}
              />
            </Box>
          </Grid>
        </Grid>
      </Container>
    </HeroContainer>
  );
};

export default Hero;
