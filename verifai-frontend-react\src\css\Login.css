.login-container {
  background: url('../img/bg.png') center center/cover no-repeat;
  background-color: rgb(39, 50, 98);
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: inset 0 0 0 1000px rgba(0, 0, 0, 0.2);
  object-fit: contain;
  justify-content: center;

}

.form-box {
  width: 400px;
  height: 500px;
  background-color: #e3eefc;
  opacity: 0.8;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  border-radius: 2%;
  position: absolute;
  align-items: center;
}

.form-box>h1 {
  color: #fff;
  top: 4.7em;
  margin-top: 50px;
  font-size: 65px;
  text-transform: uppercase;
  font-family: 'Gambetta', serif;
  letter-spacing: -3px;
  transition: 700ms ease;
  font-variation-settings: "wght" 311;
  margin-bottom: 0.8rem;
  outline: none;
  text-align: center;
}

/* h1:hover {
    font-variation-settings: "wght" 582; 
    letter-spacing: 1px;
  }
   */
.form-box>h2 {
  color: #193453;
  font-family: 'Gambet<PERSON>', serif;
  letter-spacing: 0.5px;
  font-size: 18px;
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
}

.form{
  width: 400px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.container>p {
  margin-top: 15px;
  margin-bottom: 10px;
  color: #fff;
  font-size: 32px;
  font-family: Georgia, serif;
  /* font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande',
      'Lucida Sans', Arial, sans-serif; */
  line-height: 150%;
  text-align: center;
  color: MintCream;
  letter-spacing: .5px;
}

label {
  color: #193453;
  font-family: 'Gambetta', serif;
  font-size: 15px;
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
}

input.login {
  width: 100%;
  top: 4.7em;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #ffffff;
  border: none;
  outline: none;
  padding: 10px;
  font-size: 13px;
  color: rgba(0, 0, 0);
  border-radius: 40px;
}

input.login:focus {
  box-shadow: inset 0 -5px 45px rgba(100, 100, 100, 0.4), 0 1px 1px rgba(255, 255, 255, 0.2);
}

