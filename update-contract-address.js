#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'project-config.json');
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
    } catch (error) {
        log(`❌ Error loading config: ${error.message}`, 'red');
        process.exit(1);
    }
}

function updateContractConfig(config) {
    const configPath = 'verifai-frontend-react/src/utils/contractConfig.js';
    const content = `// Auto-generated contract configuration - DO NOT EDIT MANUALLY
// This file is automatically updated by update-contract-address.js
export const CONTRACT_ADDRESS = "${config.blockchain.contractAddress}";
export const NETWORK_CONFIG = {
    chainId: ${config.blockchain.network.chainId},
    chainName: "${config.blockchain.network.name}",
    rpcUrl: "${config.blockchain.network.rpcUrl}",
    nativeCurrency: {
        name: "${config.blockchain.network.currency.name}",
        symbol: "${config.blockchain.network.currency.symbol}",
        decimals: ${config.blockchain.network.currency.decimals}
    }
};

export const GANACHE_ACCOUNTS = [
${config.blockchain.ganacheAccounts.map(account => `    "${account}"`).join(',\n')}
];

export default {
    CONTRACT_ADDRESS,
    NETWORK_CONFIG,
    GANACHE_ACCOUNTS
};
`;

    try {
        fs.writeFileSync(configPath, content);
        log(`✅ Updated: ${configPath}`, 'green');
        return true;
    } catch (error) {
        log(`❌ Failed to update ${configPath}: ${error.message}`, 'red');
        return false;
    }
}

function updateReactComponent(filePath, config) {
    try {
        if (!fs.existsSync(filePath)) {
            log(`⚠️  File not found: ${filePath}`, 'yellow');
            return false;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        
        // Pattern to match CONTRACT_ADDRESS declarations
        const patterns = [
            /const CONTRACT_ADDRESS = process\.env\.REACT_APP_CONTRACT_ADDRESS \|\| "[^"]+";/g,
            /const CONTRACT_ADDRESS = "[^"]+";/g,
            /CONTRACT_ADDRESS = "[^"]+"/g
        ];

        let updated = false;
        patterns.forEach(pattern => {
            if (pattern.test(content)) {
                content = content.replace(pattern, 
                    `const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || "${config.blockchain.contractAddress}";`
                );
                updated = true;
            }
        });

        if (updated) {
            fs.writeFileSync(filePath, content);
            log(`✅ Updated: ${filePath}`, 'green');
            return true;
        } else {
            log(`ℹ️  No updates needed: ${filePath}`, 'cyan');
            return true;
        }
    } catch (error) {
        log(`❌ Failed to update ${filePath}: ${error.message}`, 'red');
        return false;
    }
}

function updateSmartContractScript(filePath, config) {
    try {
        if (!fs.existsSync(filePath)) {
            log(`⚠️  File not found: ${filePath}`, 'yellow');
            return false;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        
        // Pattern to match contract address in scripts
        const pattern = /const contractAddress = "[^"]+";/g;
        
        if (pattern.test(content)) {
            content = content.replace(pattern, 
                `const contractAddress = "${config.blockchain.contractAddress}";`
            );
            fs.writeFileSync(filePath, content);
            log(`✅ Updated: ${filePath}`, 'green');
            return true;
        } else {
            log(`ℹ️  No updates needed: ${filePath}`, 'cyan');
            return true;
        }
    } catch (error) {
        log(`❌ Failed to update ${filePath}: ${error.message}`, 'red');
        return false;
    }
}

function updateHtmlFile(filePath, config) {
    try {
        if (!fs.existsSync(filePath)) {
            log(`⚠️  File not found: ${filePath}`, 'yellow');
            return false;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        
        // Pattern to match CONTRACT_ADDRESS in HTML files
        const patterns = [
            /const CONTRACT_ADDRESS = "[^"]+";/g,
            /const contractAddress = '[^']+';/g,
            /contractAddress = '[^']+'/g
        ];

        let updated = false;
        patterns.forEach(pattern => {
            if (pattern.test(content)) {
                content = content.replace(pattern, 
                    `const CONTRACT_ADDRESS = "${config.blockchain.contractAddress}";`
                );
                updated = true;
            }
        });

        if (updated) {
            fs.writeFileSync(filePath, content);
            log(`✅ Updated: ${filePath}`, 'green');
            return true;
        } else {
            log(`ℹ️  No updates needed: ${filePath}`, 'cyan');
            return true;
        }
    } catch (error) {
        log(`❌ Failed to update ${filePath}: ${error.message}`, 'red');
        return false;
    }
}

function updateJsonFile(filePath, config) {
    try {
        if (!fs.existsSync(filePath)) {
            log(`⚠️  File not found: ${filePath}`, 'yellow');
            return false;
        }

        const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        if (jsonData.contractAddress) {
            jsonData.contractAddress = config.blockchain.contractAddress;
            jsonData.deploymentTime = new Date().toISOString();
            
            fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2));
            log(`✅ Updated: ${filePath}`, 'green');
            return true;
        } else {
            log(`ℹ️  No contract address field found: ${filePath}`, 'cyan');
            return true;
        }
    } catch (error) {
        log(`❌ Failed to update ${filePath}: ${error.message}`, 'red');
        return false;
    }
}

function updateDocumentationFile(filePath, config) {
    try {
        if (!fs.existsSync(filePath)) {
            log(`⚠️  File not found: ${filePath}`, 'yellow');
            return false;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        
        // Pattern to match contract addresses in documentation
        const pattern = /0x[a-fA-F0-9]{40}/g;
        const matches = content.match(pattern);
        
        if (matches && matches.length > 0) {
            // Replace old contract addresses with new one
            matches.forEach(oldAddress => {
                if (oldAddress !== config.blockchain.contractAddress) {
                    content = content.replace(new RegExp(oldAddress, 'g'), config.blockchain.contractAddress);
                }
            });
            
            fs.writeFileSync(filePath, content);
            log(`✅ Updated: ${filePath}`, 'green');
            return true;
        } else {
            log(`ℹ️  No contract addresses found: ${filePath}`, 'cyan');
            return true;
        }
    } catch (error) {
        log(`❌ Failed to update ${filePath}: ${error.message}`, 'red');
        return false;
    }
}

function main() {
    log('🚀 Starting Contract Address Update Process...', 'bright');
    log('', 'reset');
    
    const config = loadConfig();
    log(`📋 Current contract address: ${config.blockchain.contractAddress}`, 'blue');
    log(`📋 Network: ${config.blockchain.network.name} (Chain ID: ${config.blockchain.network.chainId})`, 'blue');
    log('', 'reset');
    
    let successCount = 0;
    let totalFiles = 0;
    
    // Update contract config first
    log('📄 Updating contract configuration...', 'magenta');
    if (updateContractConfig(config)) successCount++;
    totalFiles++;
    
    // Update React components
    log('⚛️  Updating React components...', 'magenta');
    config.files.frontend.forEach(filePath => {
        if (filePath.endsWith('.jsx')) {
            if (updateReactComponent(filePath, config)) successCount++;
        } else if (filePath.endsWith('.html')) {
            if (updateHtmlFile(filePath, config)) successCount++;
        }
        totalFiles++;
    });
    
    // Update smart contract scripts
    log('📜 Updating smart contract scripts...', 'magenta');
    config.files.smartContract.forEach(filePath => {
        if (filePath.endsWith('.js')) {
            if (updateSmartContractScript(filePath, config)) successCount++;
        } else if (filePath.endsWith('.json')) {
            if (updateJsonFile(filePath, config)) successCount++;
        }
        totalFiles++;
    });
    
    log('', 'reset');
    log(`✅ Update complete! ${successCount}/${totalFiles} files processed successfully.`, 'green');
    log('', 'reset');
    log('🎯 Next steps:', 'bright');
    log('1. Test your application to ensure all components use the new address', 'cyan');
    log('2. Restart your development servers if they are running', 'cyan');
    log('3. Clear browser cache if needed', 'cyan');
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    loadConfig,
    updateContractConfig,
    updateReactComponent,
    updateSmartContractScript,
    updateHtmlFile,
    updateJsonFile
};
