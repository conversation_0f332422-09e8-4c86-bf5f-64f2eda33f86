const hre = require("hardhat");

async function main() {
    console.log("🚀 Deploying Verifai contract to Ganache...\n");

    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        console.log("✅ Contract factory created");

        // Deploy the contract
        console.log("⏳ Deploying contract...");
        const verifaiContract = await VerifaiFactory.deploy();

        // Wait for deployment
        await verifaiContract.deployed();
        console.log("✅ Verifai contract deployed successfully!");
        console.log("📍 Contract address:", verifaiContract.address);

        // Get deployer info
        const [deployer] = await hre.ethers.getSigners();
        console.log("👤 Deployed by:", deployer.address);

        // Test contract
        console.log("\n🔍 Testing contract...");
        try {
            // Try to call a simple function to verify deployment
            const tx = await verifaiContract.registerProduct(
                "Test Product",
                "Test Brand", 
                "TEST123",
                "Test Description",
                "test.jpg",
                "Test Manufacturer",
                "Test Location",
                Math.floor(Date.now() / 1000).toString()
            );
            await tx.wait();
            console.log("✅ Test registration successful!");
            
            // Try to get the product
            const product = await verifaiContract.getProduct("TEST123");
            console.log("✅ Product retrieval successful!");
            console.log("📦 Product name:", product.name);
        } catch (error) {
            console.log("⚠️ Contract test failed:", error.message);
        }

        console.log("\n🎉 Deployment Summary:");
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        console.log(`📍 Contract Address: ${verifaiContract.address}`);
        console.log(`🌐 Network: Ganache (Chain ID: 1337)`);
        console.log(`🔗 RPC URL: http://127.0.0.1:7545`);
        console.log(`👤 Deployer: ${deployer.address}`);
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        console.log("\n📋 MetaMask Configuration:");
        console.log("1. Add Ganache network to MetaMask:");
        console.log("   - Network Name: Ganache Local");
        console.log("   - RPC URL: http://127.0.0.1:7545");
        console.log("   - Chain ID: 1337");
        console.log("   - Currency Symbol: ETH");
        console.log("\n2. Import Ganache account:");
        console.log("   - Private Key: 0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d");
        console.log("   - This account has 100 ETH");
        console.log("\n3. Update frontend contract address:");
        console.log(`   - CONTRACT_ADDRESS = "${verifaiContract.address}"`);

        return verifaiContract.address;

    } catch (error) {
        console.error("❌ Deployment failed:", error.message);
        console.error("Full error:", error);
        process.exit(1);
    }
}

// Execute deployment
if (require.main === module) {
    main()
        .then((address) => {
            console.log(`\n✅ Deployment completed successfully!`);
            console.log(`Contract deployed at: ${address}`);
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Deployment script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
