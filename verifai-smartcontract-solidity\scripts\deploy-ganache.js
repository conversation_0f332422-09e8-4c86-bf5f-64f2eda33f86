const hre = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
    console.log("🚀 Deploying Verifai contract to Ganache...\n");

    try {
        // Get the deployer account
        const [deployer] = await hre.ethers.getSigners();
        console.log("📋 Deploying contracts with account:", deployer.address);

        // Check account balance
        const balance = await deployer.getBalance();
        console.log("💰 Account balance:", hre.ethers.utils.formatEther(balance), "ETH");

        if (balance.lt(hre.ethers.utils.parseEther("1"))) {
            throw new Error("Insufficient balance for deployment. Need at least 1 ETH.");
        }

        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        console.log("✅ Contract factory created");

        // Deploy the contract with gas settings optimized for Ganache
        console.log("⏳ Deploying contract...");
        const verifaiContract = await VerifaiFactory.deploy({
            gasLimit: 3000000,  // 3M gas limit
            gasPrice: hre.ethers.utils.parseUnits("20", "gwei") // 20 gwei
        });

        // Wait for deployment
        await verifaiContract.deployed();
        const contractAddress = verifaiContract.address;

        console.log("✅ Verifai contract deployed successfully!");
        console.log("📍 Contract address:", contractAddress);

        // Verify deployment by calling a contract method
        console.log("\n🔍 Verifying deployment...");
        try {
            // Test contract is working by checking if we can call a view function
            const contractName = await verifaiContract.name ? await verifaiContract.name() : "Verifai";
            console.log("✅ Contract verification successful");
        } catch (error) {
            console.log("⚠️ Contract deployed but verification failed:", error.message);
        }

        // Save deployment info
        const deploymentInfo = {
            network: "ganache",
            contractAddress: contractAddress,
            deployerAddress: deployer.address,
            deploymentTime: new Date().toISOString(),
            gasUsed: "Estimated 3M",
            chainId: 1337
        };

        // Save to file for frontend use
        const deploymentPath = path.join(__dirname, '../deployments/ganache.json');
        const deploymentDir = path.dirname(deploymentPath);
        
        if (!fs.existsSync(deploymentDir)) {
            fs.mkdirSync(deploymentDir, { recursive: true });
        }
        
        fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
        console.log("📄 Deployment info saved to:", deploymentPath);

        // Update frontend contract address
        const frontendConfigPath = path.join(__dirname, '../../verifai-frontend-react/src/utils/contractConfig.js');
        const contractConfig = `// Auto-generated contract configuration for Ganache
export const CONTRACT_ADDRESS = "${contractAddress}";
export const NETWORK_CONFIG = {
    chainId: 1337,
    chainName: "Ganache Local",
    rpcUrl: "http://127.0.0.1:7545",
    nativeCurrency: {
        name: "Ethereum",
        symbol: "ETH",
        decimals: 18
    }
};

export const GANACHE_ACCOUNTS = [
    "******************************************", // Account 0
    "******************************************", // Account 1
    "******************************************", // Account 2
    "******************************************", // Account 3
    "******************************************"  // Account 4
];

export default {
    CONTRACT_ADDRESS,
    NETWORK_CONFIG,
    GANACHE_ACCOUNTS
};
`;

        try {
            fs.writeFileSync(frontendConfigPath, contractConfig);
            console.log("✅ Frontend configuration updated");
        } catch (error) {
            console.log("⚠️ Could not update frontend config:", error.message);
        }

        // Update centralized project configuration
        try {
            const projectConfigPath = path.join(__dirname, '../../project-config.json');
            if (fs.existsSync(projectConfigPath)) {
                const projectConfig = JSON.parse(fs.readFileSync(projectConfigPath, 'utf8'));
                projectConfig.blockchain.contractAddress = contractAddress;
                projectConfig.deployment.lastUpdated = new Date().toISOString();
                projectConfig.deployment.deployedBy = 'deploy-ganache.js';
                projectConfig.deployment.gasUsed = 'Estimated 3M';
                projectConfig.deployment.transactionHash = '';

                fs.writeFileSync(projectConfigPath, JSON.stringify(projectConfig, null, 2));
                console.log("✅ Centralized project configuration updated");

                // Run the update script to propagate changes
                const { execSync } = require('child_process');
                try {
                    execSync('node ../../update-contract-address.js', { cwd: __dirname, stdio: 'inherit' });
                    console.log("✅ All project files updated automatically");
                } catch (updateError) {
                    console.log("⚠️ Could not run automatic update script:", updateError.message);
                    console.log("💡 Run manually: node update-contract-address.js");
                }
            } else {
                console.log("⚠️ Project config not found, skipping centralized update");
            }
        } catch (error) {
            console.log("⚠️ Could not update centralized config:", error.message);
        }

        console.log("\n🎉 Deployment Summary:");
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        console.log(`📍 Contract Address: ${contractAddress}`);
        console.log(`🌐 Network: Ganache (Chain ID: 1337)`);
        console.log(`🔗 RPC URL: http://127.0.0.1:7545`);
        console.log(`👤 Deployer: ${deployer.address}`);
        console.log(`💰 Balance: ${hre.ethers.utils.formatEther(balance)} ETH`);
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        console.log("\n📋 Next Steps:");
        console.log("1. Configure MetaMask to connect to Ganache:");
        console.log("   - Network Name: Ganache Local");
        console.log("   - RPC URL: http://127.0.0.1:7545");
        console.log("   - Chain ID: 1337");
        console.log("   - Currency Symbol: ETH");
        console.log("\n2. Import Ganache accounts to MetaMask using private keys");
        console.log("3. Test product registration in the frontend");

        return contractAddress;

    } catch (error) {
        console.error("❌ Deployment failed:", error.message);
        console.error("Full error:", error);
        process.exit(1);
    }
}

// Execute deployment
if (require.main === module) {
    main()
        .then((address) => {
            console.log(`\n✅ Deployment completed successfully!`);
            console.log(`Contract deployed at: ${address}`);
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Deployment script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
