# 🦊 MetaMask Configuration Guide for Verifai

## 🔍 **Root Cause Analysis**

Your cascading issues are caused by **MetaMask network configuration problems**:

1. **Product Registration Hangs** → MetaMask not connected to local Hardhat network
2. **Axios Login Issues** → Frontend timeouts due to blockchain connection failures  
3. **Backend Connection Problems** → Session management breaks due to frontend errors

## 🚀 **Complete Solution**

### **Step 1: Add Hardhat Network to MetaMask**

#### **Manual Configuration:**
1. Open MetaMask extension
2. Click the network dropdown (top center)
3. Click "Add Network" or "Custom RPC"
4. Enter these **exact** details:

```
Network Name: Hardhat Local
New RPC URL: http://127.0.0.1:8545
Chain ID: 1337
Currency Symbol: ETH
Block Explorer URL: (leave empty)
```

#### **Automatic Configuration (Recommended):**
Click this button in your browser console:
```javascript
// Add Hardhat network automatically
await window.ethereum.request({
  method: 'wallet_addEthereumChain',
  params: [{
    chainId: '0x539', // 1337 in hex
    chainName: 'Hardhat Local',
    rpcUrls: ['http://127.0.0.1:8545'],
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18
    }
  }]
});
```

### **Step 2: Import Hardhat Test Account**

1. Copy this private key: `0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80`
2. In MetaMask: Click account icon → Import Account → Private Key
3. Paste the private key and import
4. This account has 10,000 ETH for testing

### **Step 3: Switch to Hardhat Network**

1. In MetaMask, click the network dropdown
2. Select "Hardhat Local"
3. Verify you see ~10,000 ETH balance

### **Step 4: Verify Configuration**

Visit: `http://localhost:5173/test-metamask.html`

Expected results:
- ✅ MetaMask detected
- ✅ Connected to account: ******************************************
- ✅ Chain ID: 0x539 (1337)
- ✅ Connected to Hardhat network
- ✅ Contract found at ******************************************

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: "Chain ID Mismatch"**
**Solution:** Ensure Chain ID is exactly `1337` (not 31337)

### **Issue 2: "RPC URL Not Working"**
**Solution:** 
- Use `http://127.0.0.1:8545` (not localhost)
- Ensure Hardhat node is running: `npx hardhat node`

### **Issue 3: "Contract Not Found"**
**Solution:**
- Redeploy contract: `npx hardhat run scripts/deploy.js --network localhost`
- Update contract address in frontend

### **Issue 4: "Transaction Fails"**
**Solution:**
- Reset MetaMask account: Settings → Advanced → Reset Account
- Clear browser cache and reload

## 🎯 **Quick Fix Commands**

```bash
# 1. Start Hardhat node (if not running)
cd verifai-smartcontract-solidity
npx hardhat node

# 2. Deploy contract (new terminal)
npx hardhat run scripts/deploy.js --network localhost

# 3. Test connection
node scripts/diagnose-registration.js
```

## ✅ **Verification Checklist**

- [ ] Hardhat node running on port 8545
- [ ] MetaMask connected to "Hardhat Local" network
- [ ] Using test account with 10,000 ETH
- [ ] Contract deployed at correct address
- [ ] Test page shows all green checkmarks
- [ ] Product registration works without hanging

## 🚨 **Emergency Reset**

If nothing works:

1. **Reset MetaMask:** Settings → Advanced → Reset Account
2. **Clear browser data:** F12 → Application → Storage → Clear
3. **Restart everything:**
   ```bash
   # Kill all processes
   taskkill /f /im node.exe
   
   # Restart Hardhat
   cd verifai-smartcontract-solidity
   npx hardhat node
   
   # Redeploy (new terminal)
   npx hardhat run scripts/deploy.js --network localhost
   
   # Restart backend
   cd ../Verifai-backend
   node postgres.js
   
   # Restart frontend
   cd ../verifai-frontend-react
   npm run vite
   ```

## 📱 **Mobile Testing**

For mobile MetaMask:
1. Use your computer's IP instead of localhost
2. Example: `http://*************:8545`
3. Ensure firewall allows connections

## 🔐 **Security Notes**

- **Never use test private keys on mainnet**
- **Test accounts are for development only**
- **Reset accounts before production deployment**

---

**After following this guide, your product registration should work instantly without hanging, and all authentication issues should be resolved.**
