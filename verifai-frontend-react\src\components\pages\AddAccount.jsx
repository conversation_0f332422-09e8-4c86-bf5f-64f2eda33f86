import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Autocomplete,
  Card,
  CardMedia,
  Divider,
  Alert,
  CircularProgress,
  Grid,
  Stack,
  IconButton,
  Container,
  useTheme,
  alpha,
  ThemeProvider,
  InputAdornment
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  ArrowBack,
  Home,
  Security,
  PersonAdd,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import createAppTheme from '../../theme/index';
import ParticleField from '../common/ParticleField';

const roles = ["manufacturer", "supplier", "retailer"];

// Professional animations
const smoothSlideIn = keyframes`
  0% {
    transform: translateY(40px);
    opacity: 0;
    filter: blur(10px);
  }
  100% {
    transform: translateY(0);
    opacity: 1;
    filter: blur(0);
  }
`;

const gentleFloat = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
`;

// Professional styled components
const FuturisticContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: theme.palette.mode === 'dark' ? '#121212' : '#ffffff',
  position: 'relative',
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  py: 4,
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '24px',
  boxShadow: `
    0 8px 32px ${alpha(theme.palette.common.black, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
  `,
  animation: `${gentleFloat} 6s ease-in-out infinite`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `
      0 20px 40px ${alpha(theme.palette.common.black, 0.15)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.3)}
    `,
  },
}));

const GradientText = styled(Typography)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  fontWeight: 800,
  letterSpacing: '-0.02em',
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
}));

const NeonAvatar = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  boxShadow: `
    0 0 30px ${alpha(theme.palette.primary.main, 0.5)},
    0 0 60px ${alpha(theme.palette.primary.main, 0.3)},
    inset 0 2px 0 ${alpha(theme.palette.common.white, 0.3)}
  `,
  animation: `${gentleFloat} 4s ease-in-out infinite`,
  '& .MuiSvgIcon-root': {
    fontSize: '2rem',
    color: 'white',
  },
}));

const CyberButton = styled(Button)(({ theme, variant = 'primary' }) => ({
  borderRadius: '16px',
  padding: '12px 32px',
  fontWeight: 700,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  background: variant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: variant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: variant === 'secondary' ? `2px solid ${alpha(theme.palette.primary.main, 0.3)}` : 'none',
  boxShadow: variant === 'primary'
    ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 15px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: variant === 'primary'
      ? `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.common.black, 0.15)}`,
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  color: theme.palette.primary.main,
  marginBottom: theme.spacing(2),
  fontSize: '1.2rem',
}));

const NavigationHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
  paddingBottom: theme.spacing(2),
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
}));

// Animated Eye Button for password visibility
const AnimatedEyeButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'isVisible'
})(({ theme, isVisible }) => ({
  color: isVisible ? theme.palette.primary.main : theme.palette.text.secondary,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  borderRadius: '12px',
  padding: '8px',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    color: theme.palette.primary.main,
    transform: 'scale(1.1)',
  },
  '&:active': {
    transform: 'scale(0.95)',
  },
  '& .MuiSvgIcon-root': {
    fontSize: '1.2rem',
    transition: 'all 0.2s ease-in-out',
  },
}));

// Enhanced TextField with futuristic styling
const FuturisticTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    backgroundColor: alpha(theme.palette.background.paper, 0.8),
    '&:hover': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: alpha(theme.palette.primary.main, 0.5),
        boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.1)}`,
      },
    },
    '&.Mui-focused': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.2)}`,
      },
    },
  },
  '& .MuiInputLabel-root': {
    fontWeight: 500,
    '&.Mui-focused': {
      color: theme.palette.primary.main,
    },
  },
}));

const AddAccount = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    role: roles[0],
    name: '',
    description: '',
    website: '',
    location: ''
  });
  const [image, setImage] = useState({ file: null, filepreview: null });
  const [status, setStatus] = useState({
    error: null,
    success: null,
    loading: false
  });
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false
  });

  // Password visibility states
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const navigate = useNavigate();
  const fileInputRef = useRef();

  useEffect(() => {
    // Password validation checks
    const password = formData.password;
    setPasswordValidation({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      specialChar: /[@$!%*?&]/.test(password)
    });
  }, [formData.password]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Password visibility toggle functions with smooth animations
  const handleTogglePasswordVisibility = () => {
    if (isToggling) return;

    setIsToggling(true);
    setShowPassword(!showPassword);

    // Focus restoration for better UX
    setTimeout(() => {
      const passwordField = document.querySelector('input[name="password"]');
      if (passwordField) {
        passwordField.focus();
        const length = passwordField.value.length;
        passwordField.setSelectionRange(length, length);
      }
      setIsToggling(false);
    }, 100);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    if (isToggling) return;

    setIsToggling(true);
    setShowConfirmPassword(!showConfirmPassword);

    // Focus restoration for better UX
    setTimeout(() => {
      const confirmPasswordField = document.querySelector('input[name="confirmPassword"]');
      if (confirmPasswordField) {
        confirmPasswordField.focus();
        const length = confirmPasswordField.value.length;
        confirmPasswordField.setSelectionRange(length, length);
      }
      setIsToggling(false);
    }, 100);
  };

  const handleImageUpload = (e) => {
    if (e.target.files?.[0]) {
      setImage({
        file: e.target.files[0],
        filepreview: URL.createObjectURL(e.target.files[0]),
      });
    }
  };

  const uploadImage = async () => {
    const data = new FormData();
    data.append("image", image.file);
    try {
      await axios.post("http://localhost:3000/upload/profile", data, {
        headers: { "Content-Type": "multipart/form-data" }
      });
      return true;
    } catch (error) {
      console.error("Image upload error:", error);
      return false;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setStatus({ ...status, loading: true });

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setStatus({
        error: 'Passwords do not match',
        success: null,
        loading: false
      });
      return;
    }

    // Validate password strength
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(formData.password)) {
      setStatus({
        error: 'Password does not meet requirements',
        success: null,
        loading: false
      });
      return;
    }

    try {
      // Create account
      const accountRes = await axios.post('http://localhost:3000/addaccount', {
        username: formData.username,
        password: formData.password,
        role: formData.role
      }, {
        headers: { 'Content-Type': 'application/json' },
      });

      // Create profile
      await axios.post('http://localhost:3000/addprofile', {
        username: formData.username,
        name: formData.name,
        description: formData.description,
        website: formData.website,
        location: formData.location,
        image: image.file?.name || '',
        role: formData.role
      }, {
        headers: { 'Content-Type': 'application/json' },
      });

      // Upload image if provided
      if (image.file) {
        await uploadImage();
      }

      setStatus({
        error: null,
        success: 'Account created successfully!',
        loading: false
      });

      // Reset form after successful submission
      setFormData({
        username: '',
        password: '',
        confirmPassword: '',
        role: roles[0],
        name: '',
        description: '',
        website: '',
        location: ''
      });
      setImage({ file: null, filepreview: null });

    } catch (error) {
      console.error("Account creation error:", error);
      const errorMessage = error.response?.data?.message || 
                         (error.response?.status === 400 ? 'Invalid username or password' : 
                         'Account creation failed. Please try again.');
      setStatus({
        error: errorMessage,
        success: null,
        loading: false
      });
    }
  };

  const handleBack = () => navigate(-1);
  const handleBackToLogin = () => navigate('/login');
  const handleBackToHome = () => navigate('/');
  const theme = createAppTheme('light');

  return (
    <ThemeProvider theme={theme}>
      <FuturisticContainer>
        <ParticleField density="low" animated={true} />

        <Container maxWidth="md" sx={{ position: 'relative', zIndex: 2 }}>
          <GlassCard sx={{
            maxWidth: 800,
            width: '100%',
            p: 6,
            mx: 'auto',
            animation: `${smoothSlideIn} 1s cubic-bezier(0.4, 0, 0.2, 1)`,
          }}>
            {/* Header Section */}
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              mb: 4
            }}>
              <NeonAvatar sx={{ mb: 3 }}>
                <PersonAdd />
              </NeonAvatar>

              <GradientText variant="h3" component="h1" sx={{ mb: 1 }}>
                Create Account
              </GradientText>

              <Typography variant="h6" component="h2" color="text.secondary" sx={{ mb: 2, textAlign: 'center' }}>
                Register a new manufacturer, supplier, or retailer account
              </Typography>
            </Box>
            <NavigationHeader>
              <CyberButton
                variant="secondary"
                startIcon={<ArrowBack />}
                onClick={handleBackToLogin}
                sx={{ px: 3, py: 1 }}
              >
                Back to Login
              </CyberButton>
              <IconButton
                onClick={handleBackToHome}
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1)
                  }
                }}
                aria-label="home"
              >
                <Home />
              </IconButton>
            </NavigationHeader>

            <Divider sx={{ my: 4, borderColor: alpha(theme.palette.divider, 0.2) }} />
        
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <SectionTitle variant="h6">Account Details</SectionTitle>
              <Stack spacing={2}>
                <FuturisticTextField
                  fullWidth
                  label="Username *"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                />
                
                <FuturisticTextField
                  fullWidth
                  label="Password *"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <AnimatedEyeButton
                            aria-label={showPassword ? "Hide password" : "Show password"}
                            onClick={handleTogglePasswordVisibility}
                            onMouseDown={(e) => e.preventDefault()}
                            disabled={isToggling}
                            isVisible={showPassword}
                            edge="end"
                            title={showPassword ? "Hide password" : "Show password"}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </AnimatedEyeButton>
                        </InputAdornment>
                      )
                    }
                  }}
                />
                
                <Box sx={{ pl: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Password must contain:
                  </Typography>
                  <Box component="ul" sx={{ pl: 2, mt: 0.5, mb: 0 }}>
                    <Box component="li" sx={{ color: passwordValidation.length ? 'success.main' : 'text.secondary' }}>
                      At least 8 characters
                    </Box>
                    <Box component="li" sx={{ color: passwordValidation.uppercase ? 'success.main' : 'text.secondary' }}>
                      One uppercase letter
                    </Box>
                    <Box component="li" sx={{ color: passwordValidation.lowercase ? 'success.main' : 'text.secondary' }}>
                      One lowercase letter
                    </Box>
                    <Box component="li" sx={{ color: passwordValidation.number ? 'success.main' : 'text.secondary' }}>
                      One number
                    </Box>
                    <Box component="li" sx={{ color: passwordValidation.specialChar ? 'success.main' : 'text.secondary' }}>
                      One special character (@$!%*?&)
                    </Box>
                  </Box>
                </Box>
                
                <FuturisticTextField
                  fullWidth
                  label="Confirm Password *"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  error={formData.password !== formData.confirmPassword && formData.confirmPassword !== ''}
                  helperText={formData.password !== formData.confirmPassword && formData.confirmPassword !== '' ? "Passwords don't match" : ""}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <AnimatedEyeButton
                            aria-label={showConfirmPassword ? "Hide confirm password" : "Show confirm password"}
                            onClick={handleToggleConfirmPasswordVisibility}
                            onMouseDown={(e) => e.preventDefault()}
                            disabled={isToggling}
                            isVisible={showConfirmPassword}
                            edge="end"
                            title={showConfirmPassword ? "Hide confirm password" : "Show confirm password"}
                          >
                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                          </AnimatedEyeButton>
                        </InputAdornment>
                      )
                    }
                  }}
                />
                
                <Autocomplete
                  options={roles}
                  value={formData.role}
                  onChange={(event, newValue) => {
                    setFormData(prev => ({ ...prev, role: newValue }));
                  }}
                  renderInput={(params) => (
                    <TextField 
                      {...params} 
                      label="Role *" 
                      variant="outlined" 
                      size="small"
                    />
                  )}
                />
              </Stack>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <SectionTitle variant="h6">Profile Information</SectionTitle>
              <Stack spacing={2}>
                <CyberButton
                  component="label"
                  variant="secondary"
                  fullWidth
                  sx={{
                    height: '120px',
                    borderStyle: 'dashed',
                    borderWidth: '2px',
                    borderColor: alpha(theme.palette.primary.main, 0.3),
                    '&:hover': {
                      borderColor: alpha(theme.palette.primary.main, 0.6),
                    }
                  }}
                >
                  {image.filepreview ? 'Change Image' : 'Upload Profile Image'}
                  <input
                    type="file"
                    hidden
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                  />
                </CyberButton>
                
                {image.filepreview && (
                  <Card elevation={0} sx={{ borderRadius: '8px', overflow: 'hidden' }}>
                    <CardMedia
                      component="img"
                      height="140"
                      image={image.filepreview}
                      alt="Profile preview"
                      sx={{ objectFit: 'cover' }}
                    />
                  </Card>
                )}
                
                <TextField
                  fullWidth
                  label="Organization Name *"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                />
                
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={3}
                  size="small"
                />
                
                <TextField
                  fullWidth
                  label="Website"
                  name="website"
                  value={formData.website}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                />
                
                <TextField
                  fullWidth
                  label="Location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                />
              </Stack>
            </Grid>
            
            <Grid item xs={12}>
              {status.error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {status.error}
                </Alert>
              )}
              
              {status.success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {status.success}
                </Alert>
              )}
              
              <Stack direction="row" spacing={3} justifyContent="center">
                <CyberButton
                  variant="secondary"
                  onClick={handleBack}
                  disabled={status.loading}
                  sx={{ px: 4, py: 1.5 }}
                >
                  Cancel
                </CyberButton>

                <CyberButton
                  variant="primary"
                  type="submit"
                  disabled={status.loading}
                  startIcon={status.loading ? <CircularProgress size={20} color="inherit" /> : <PersonAdd />}
                  sx={{ px: 4, py: 1.5 }}
                >
                  {status.loading ? 'Creating Account...' : 'Create Account'}
                </CyberButton>
              </Stack>
            </Grid>
          </Grid>
        </form>
          </GlassCard>
        </Container>
      </FuturisticContainer>
    </ThemeProvider>
  );
};

export default AddAccount;