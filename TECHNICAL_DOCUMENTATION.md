# VERIFAI Technical Documentation

## System Architecture

### Overview
VERIFAI implements a hybrid architecture combining traditional web technologies with blockchain:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  React Frontend │────▶│  Node.js API    │────▶│  PostgreSQL DB  │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  Web3.js        │────▶│  Ethereum       │
│  (Blockchain    │     │  Smart Contract │
│   Interface)    │     │                 │
└─────────────────┘     └─────────────────┘
```

### Authentication Flow
1. User submits credentials
2. Backend validates and issues JWT tokens (access + refresh)
3. Frontend stores tokens in localStorage
4. Protected routes verify token validity
5. Token refresh occurs automatically when needed

### Product Registration Flow
1. Manufacturer inputs product details
2. Data stored in PostgreSQL database
3. Product hash generated and stored on blockchain
4. QR code generated containing product ID and verification URL

### Verification Flow
1. Consumer scans QR code
2. Frontend queries backend API with product ID
3. Backend retrieves data from PostgreSQL
4. Backend verifies product hash on blockchain
5. Authentication result displayed to user

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(100) NOT NULL,
  role VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Products Table
```sql
CREATE TABLE products (
  id SERIAL PRIMARY KEY,
  serial_number VARCHAR(100) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  manufacturer VARCHAR(50) NOT NULL,
  date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  blockchain_hash VARCHAR(66),
  status VARCHAR(20) DEFAULT 'active'
);
```

## Smart Contract

The core smart contract implements:

```solidity
// ProductVerification.sol (simplified)
contract ProductVerification {
    struct Product {
        string serialNumber;
        string manufacturer;
        uint256 timestamp;
        bool exists;
    }
    
    mapping(string => Product) private products;
    
    function registerProduct(string memory serialNumber, string memory manufacturer) public {
        require(!products[serialNumber].exists, "Product already registered");
        
        products[serialNumber] = Product({
            serialNumber: serialNumber,
            manufacturer: manufacturer,
            timestamp: block.timestamp,
            exists: true
        });
    }
    
    function verifyProduct(string memory serialNumber) public view returns (bool, string memory, uint256) {
        Product memory product = products[serialNumber];
        return (product.exists, product.manufacturer, product.timestamp);
    }
}
```

## API Endpoints

### Authentication
- `POST /auth` - User login
- `POST /refresh-token` - Refresh access token

### Products
- `POST /products` - Register new product
- `GET /products/:id` - Get product details
- `GET /products` - List all products (paginated)
- `PUT /products/:id` - Update product status

### Verification
- `GET /verify/:serialNumber` - Verify product authenticity

## Security Considerations

### JWT Implementation
- Access tokens expire after 15 minutes
- Refresh tokens valid for 7 days
- Tokens stored in localStorage with proper logout handling

### Blockchain Security
- Smart contract access control via owner address
- Function modifiers to restrict sensitive operations
- Gas optimization for cost efficiency

### Web Security
- CORS configuration to prevent unauthorized access
- Rate limiting to prevent brute force attacks
- Input validation to prevent injection attacks
- HTTPS enforcement in production