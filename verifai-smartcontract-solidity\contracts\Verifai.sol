// SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.17; 

import "hardhat/console.sol";

contract Verifai{
    address public owner; 

    struct Product {
        string name;
        string serialNumber;
        string description;
        string brand;
        string image;
        mapping(uint => ProductHistory) history;
        uint historySize;
    }

    mapping(string => Product) products;
    mapping(uint => ProductHistory) history;    

    struct ProductHistory {
        uint id;
        string actor;
        string location;
        string timestamp;
        bool isSold;
    }

    function registerProduct(string memory _name, string memory _brand, string memory _serialNumber, string memory _description, string memory _image,
    string memory _actor, string memory _location, string memory _timestamp) public {
        // Check if product already exists
        require(bytes(products[_serialNumber].serialNumber).length == 0, "Product with this serial number already exists");

        Product storage p = products[_serialNumber];

        p.name = _name;
        p.brand = _brand;
        p.serialNumber = _serialNumber;
        p.description = _description;
        p.image = _image;
        p.historySize = 0;

        addProductHistory(_serialNumber,_actor, _location, _timestamp, false);
    }

    function addProductHistory(string memory _serialNumber, string memory _actor,
    string memory _location, string memory _timestamp, bool _isSold) public {
        Product storage p = products[_serialNumber];
        p.historySize++;
        p.history[p.historySize] = ProductHistory(p.historySize, _actor, _location, _timestamp, _isSold);

        console.log("i1: %s", p.historySize);
        console.log("Product History added: %s", p.history[p.historySize].actor);
        console.log("Product : %s", p.name);
    }

    function getProduct(string memory _serialNumber) public view returns (string memory, string memory,
    string memory, string memory, string memory, ProductHistory[] memory) {
        // Check if product exists
        require(bytes(products[_serialNumber].serialNumber).length > 0, "Product with this serial number does not exist");

        Product storage p = products[_serialNumber];
        ProductHistory[] memory pHistory = new ProductHistory[](p.historySize);

        for (uint i = 0; i < p.historySize; i++) {
            pHistory[i] = p.history[i+1];
        }

        return (p.serialNumber, p.name, p.brand, p.description, p.image, pHistory);
    }

    // Helper function to check if product exists
    function productExists(string memory _serialNumber) public view returns (bool) {
        return bytes(products[_serialNumber].serialNumber).length > 0;
    }

    // Helper function to get product basic info without history
    function getProductBasic(string memory _serialNumber) public view returns (string memory, string memory, string memory, string memory, string memory) {
        require(bytes(products[_serialNumber].serialNumber).length > 0, "Product with this serial number does not exist");
        Product storage p = products[_serialNumber];
        return (p.serialNumber, p.name, p.brand, p.description, p.image);
    }

}
    