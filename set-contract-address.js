#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateContractAddress(address) {
    // Check if it's a valid Ethereum address format
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    return ethAddressRegex.test(address);
}

function loadConfig() {
    try {
        const configPath = path.join(__dirname, 'project-config.json');
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
    } catch (error) {
        log(`❌ Error loading config: ${error.message}`, 'red');
        process.exit(1);
    }
}

function saveConfig(config) {
    try {
        const configPath = path.join(__dirname, 'project-config.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        return true;
    } catch (error) {
        log(`❌ Error saving config: ${error.message}`, 'red');
        return false;
    }
}

function updateContractAddress(newAddress, deploymentInfo = {}) {
    log('🔄 Updating contract address...', 'blue');
    
    // Validate the new address
    if (!validateContractAddress(newAddress)) {
        log(`❌ Invalid contract address format: ${newAddress}`, 'red');
        log('   Expected format: 0x followed by 40 hexadecimal characters', 'yellow');
        return false;
    }
    
    // Load current config
    const config = loadConfig();
    const oldAddress = config.blockchain.contractAddress;
    
    if (oldAddress === newAddress) {
        log(`ℹ️  Contract address is already set to: ${newAddress}`, 'cyan');
        return true;
    }
    
    // Update config
    config.blockchain.contractAddress = newAddress;
    config.deployment.lastUpdated = new Date().toISOString();
    config.deployment.deployedBy = deploymentInfo.deployedBy || 'manual-update';
    config.deployment.gasUsed = deploymentInfo.gasUsed || 'unknown';
    config.deployment.transactionHash = deploymentInfo.transactionHash || '';
    
    // Save updated config
    if (!saveConfig(config)) {
        return false;
    }
    
    log(`✅ Contract address updated in config:`, 'green');
    log(`   Old: ${oldAddress}`, 'yellow');
    log(`   New: ${newAddress}`, 'green');
    
    return true;
}

function runUpdateScript() {
    try {
        log('🚀 Running automatic file updates...', 'blue');
        execSync('node update-contract-address.js', { stdio: 'inherit' });
        return true;
    } catch (error) {
        log(`❌ Error running update script: ${error.message}`, 'red');
        return false;
    }
}

function showCurrentConfig() {
    const config = loadConfig();
    
    log('📋 Current Configuration:', 'bright');
    log('', 'reset');
    log(`Contract Address: ${config.blockchain.contractAddress}`, 'cyan');
    log(`Network: ${config.blockchain.network.name}`, 'cyan');
    log(`Chain ID: ${config.blockchain.network.chainId}`, 'cyan');
    log(`RPC URL: ${config.blockchain.network.rpcUrl}`, 'cyan');
    log(`Last Updated: ${config.deployment.lastUpdated}`, 'cyan');
    log(`Deployed By: ${config.deployment.deployedBy}`, 'cyan');
    log('', 'reset');
}

function showUsage() {
    log('📖 Usage:', 'bright');
    log('', 'reset');
    log('Set new contract address:', 'cyan');
    log('  node set-contract-address.js <contract-address>', 'yellow');
    log('', 'reset');
    log('Show current configuration:', 'cyan');
    log('  node set-contract-address.js --show', 'yellow');
    log('', 'reset');
    log('Update all files with current address:', 'cyan');
    log('  node set-contract-address.js --update', 'yellow');
    log('', 'reset');
    log('Examples:', 'cyan');
    log('  node set-contract-address.js 0x43E9AC257b061919b001eb5eB90212F28F4A8901', 'yellow');
    log('  node set-contract-address.js --show', 'yellow');
    log('  node set-contract-address.js --update', 'yellow');
    log('', 'reset');
}

function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        log('🔐 VERIFAI - Contract Address Manager', 'bright');
        log('', 'reset');
        showCurrentConfig();
        showUsage();
        return;
    }
    
    const command = args[0];
    
    switch (command) {
        case '--show':
        case '-s':
            showCurrentConfig();
            break;
            
        case '--update':
        case '-u':
            if (runUpdateScript()) {
                log('✅ All files updated successfully!', 'green');
            } else {
                log('❌ Update failed!', 'red');
                process.exit(1);
            }
            break;
            
        case '--help':
        case '-h':
            showUsage();
            break;
            
        default:
            // Assume it's a contract address
            const newAddress = command;
            
            log('🔐 VERIFAI - Contract Address Update', 'bright');
            log('', 'reset');
            
            if (updateContractAddress(newAddress)) {
                log('', 'reset');
                if (runUpdateScript()) {
                    log('', 'reset');
                    log('🎉 Contract address update completed successfully!', 'green');
                    log('', 'reset');
                    log('🎯 Next steps:', 'bright');
                    log('1. Restart your development servers', 'cyan');
                    log('2. Clear browser cache if needed', 'cyan');
                    log('3. Test your application', 'cyan');
                } else {
                    log('❌ File update failed!', 'red');
                    process.exit(1);
                }
            } else {
                process.exit(1);
            }
            break;
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    updateContractAddress,
    validateContractAddress,
    loadConfig,
    saveConfig,
    runUpdateScript
};
