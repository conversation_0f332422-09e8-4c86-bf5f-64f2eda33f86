import { apiPost, apiGet } from '../utils/apiUtils';

class AuthService {
    constructor() {
        this.tokenKey = 'verifai_access_token';
        this.userKey = 'verifai_user';
        this.refreshPromise = null;
    }

    // Store tokens and user data
    setAuthData(authData) {
        const { accessToken, user } = authData;
        
        if (accessToken) {
            localStorage.setItem(this.tokenKey, accessToken);
        }
        
        if (user) {
            localStorage.setItem(this.userKey, JSON.stringify(user));
        }
    }

    // Get stored access token
    getAccessToken() {
        return localStorage.getItem(this.tokenKey);
    }

    // Get stored user data
    getUser() {
        const userData = localStorage.getItem(this.userKey);
        return userData ? JSON.parse(userData) : null;
    }

    // Check if user is authenticated
    isAuthenticated() {
        const token = this.getAccessToken();
        const user = this.getUser();
        return !!(token && user);
    }

    // Login function
    async login(username, password, rememberMe = false) {
        try {
            const response = await apiPost('/auth', {
                username,
                password,
                rememberMe
            });

            // Check if this is a first-time login requiring password change
            if (response.data.requirePasswordChange) {
                return {
                    success: true,
                    requirePasswordChange: true,
                    username: response.data.username,
                    message: response.data.message || 'Password change required'
                };
            }

            // Regular successful login
            if (response.data.accessToken) {
                this.setAuthData({
                    accessToken: response.data.accessToken,
                    user: response.data.user
                });

                return {
                    success: true,
                    user: response.data.user,
                    accessToken: response.data.accessToken
                };
            }

            return {
                success: false,
                message: response.data.message || 'Login failed'
            };

        } catch (error) {
            console.error('Login error:', error);

            if (error.response?.status === 401) {
                return {
                    success: false,
                    message: 'Invalid username or password'
                };
            } else if (error.response?.status === 429) {
                return {
                    success: false,
                    message: 'Too many login attempts. Please try again later.'
                };
            }

            return {
                success: false,
                message: error.response?.data?.message || 'Login failed. Please try again.'
            };
        }
    }

    // Logout function
    async logout() {
        try {
            // Call backend logout endpoint to clear refresh token
            await apiPost('/auth/logout');
        } catch (error) {
            console.warn('Logout request failed:', error);
            // Continue with local logout even if backend call fails
        } finally {
            // Clear local storage
            this.clearAuthData();
        }
    }

    // Clear all authentication data
    clearAuthData() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        
        // Clear any other auth-related data
        localStorage.removeItem('first_time_user');
        
        // Dispatch custom event for auth state change
        window.dispatchEvent(new CustomEvent('authStateChanged', { 
            detail: { authenticated: false } 
        }));
    }

    // Refresh access token
    async refreshToken() {
        // Prevent multiple simultaneous refresh requests
        if (this.refreshPromise) {
            return this.refreshPromise;
        }

        this.refreshPromise = this._performTokenRefresh();
        
        try {
            const result = await this.refreshPromise;
            return result;
        } finally {
            this.refreshPromise = null;
        }
    }

    async _performTokenRefresh() {
        try {
            const response = await apiPost('/auth/refresh');
            
            if (response.data.accessToken) {
                // Update stored access token
                localStorage.setItem(this.tokenKey, response.data.accessToken);
                
                return {
                    success: true,
                    accessToken: response.data.accessToken
                };
            }

            throw new Error('No access token in refresh response');

        } catch (error) {
            console.error('Token refresh failed:', error);
            
            // If refresh fails, clear auth data and redirect to login
            this.clearAuthData();
            
            return {
                success: false,
                error: error.response?.data?.code || 'REFRESH_FAILED'
            };
        }
    }

    // Verify current token
    async verifyToken() {
        try {
            const token = this.getAccessToken();
            
            if (!token) {
                return { valid: false, error: 'NO_TOKEN' };
            }

            const response = await apiGet('/auth/verify');
            
            return {
                valid: true,
                user: response.data.user
            };

        } catch (error) {
            console.error('Token verification failed:', error);
            
            if (error.response?.status === 401) {
                // Token expired or invalid, try to refresh
                const refreshResult = await this.refreshToken();
                
                if (refreshResult.success) {
                    // Retry verification with new token
                    return this.verifyToken();
                }
            }

            return {
                valid: false,
                error: error.response?.data?.code || 'VERIFICATION_FAILED'
            };
        }
    }

    // Get authorization header for API requests
    getAuthHeader() {
        const token = this.getAccessToken();
        return token ? { Authorization: `Bearer ${token}` } : {};
    }

    // Check if user has specific role
    hasRole(role) {
        const user = this.getUser();
        return user?.role === role;
    }

    // Check if user has any of the specified roles
    hasAnyRole(roles) {
        const user = this.getUser();
        return roles.includes(user?.role);
    }

    // Auto-refresh token before expiration
    startTokenRefreshTimer() {
        // Refresh token 2 minutes before expiration (13 minutes for 15-minute tokens)
        const refreshInterval = 13 * 60 * 1000; // 13 minutes
        
        setInterval(async () => {
            if (this.isAuthenticated()) {
                await this.refreshToken();
            }
        }, refreshInterval);
    }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
