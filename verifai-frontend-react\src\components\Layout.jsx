
import { Outlet } from 'react-router-dom';
import { Box, CssBaseline, ThemeProvider } from '@mui/material';
import AppNavbar from './navigation/AppNavbar';
import TechBackground from './common/TechBackground';
import RateLimitIndicator from './common/RateLimitIndicator';
import createAppTheme from '../theme';

const Layout = () => {
  const theme = createAppTheme('light');

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TechBackground />
      <RateLimitIndicator />
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', position: 'relative' }}>
        <AppNavbar />
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            backgroundColor: 'transparent',
            minHeight: 'calc(100vh - 64px)',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <Outlet />
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default Layout;
