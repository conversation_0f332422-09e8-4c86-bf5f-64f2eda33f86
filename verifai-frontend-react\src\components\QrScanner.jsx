import { useEffect, useRef, useState } from 'react';
import { Html5Qrcode, Html5QrcodeScannerState } from 'html5-qrcode';
import {
  Box,
  Button,
  Typography,
  IconButton,
  Fade,
  Alert,
  Stack,
  alpha,
  useTheme
} from '@mui/material';
import {
  CameraAlt,
  Upload,
  Refresh,
  Close,
  FlashOn,
  FlashOff,
  CenterFocusStrong
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';

// Professional scanning animations with blue theme
const scanLine = keyframes`
  0% {
    transform: translateY(-100%);
    opacity: 0;
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.8);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 40px rgba(25, 118, 210, 1);
  }
  100% {
    transform: translateY(300px);
    opacity: 0;
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.8);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 0.9;
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
    box-shadow: 0 0 30px rgba(25, 118, 210, 0.5);
  }
  100% {
    transform: scale(1);
    opacity: 0.9;
    box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
  }
`;

const cornerGlow = keyframes`
  0%, 100% {
    box-shadow: 0 0 15px rgba(25, 118, 210, 0.6);
    border-color: rgba(25, 118, 210, 0.8);
  }
  50% {
    box-shadow: 0 0 25px rgba(25, 118, 210, 0.9);
    border-color: rgba(25, 118, 210, 1);
  }
`;

const ScannerContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  maxWidth: '450px',
  margin: '0 auto',
  borderRadius: '24px',
  overflow: 'hidden',
  background: `
    linear-gradient(135deg,
      ${theme.palette.mode === 'dark' ? '#0a0e1a' : '#f8faff'} 0%,
      ${theme.palette.mode === 'dark' ? '#0d1421' : '#f0f4ff'} 50%,
      ${theme.palette.mode === 'dark' ? '#0a1018' : '#f4f8ff'} 100%
    )
  `,
  border: `3px solid #1976d2`,
  boxShadow: `
    0 0 40px ${alpha('#1976d2', 0.3)},
    0 20px 60px ${alpha(theme.palette.common.black, 0.15)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
  `,
  backdropFilter: 'blur(20px) saturate(180%)',
}));

const ScannerFrame = styled(Box)(({ theme }) => ({
  position: 'relative',
  padding: '20px',
  background: `
    radial-gradient(circle at center,
      ${alpha('#1976d2', 0.05)} 0%,
      transparent 70%
    )
  `,
  '& #html5qr-code-full-region': {
    borderRadius: '20px !important',
    border: 'none !important',
    background: 'transparent !important',
    overflow: 'hidden !important',
    '& video': {
      borderRadius: '20px !important',
      objectFit: 'cover !important',
      width: '100% !important',
      height: 'auto !important',
      minHeight: '320px !important',
      border: `2px solid ${alpha('#1976d2', 0.3)} !important`,
      boxShadow: `
        0 0 30px ${alpha('#1976d2', 0.2)},
        inset 0 0 20px ${alpha(theme.palette.common.black, 0.1)}
      `,
    },
    '& canvas': {
      display: 'none !important',
    },
    '& > div': {
      border: 'none !important',
      background: 'transparent !important',
      borderRadius: '20px !important',
      '& > div': {
        border: 'none !important',
        background: 'transparent !important',
        borderRadius: '20px !important',
      },
    },
    // Hide any duplicate video elements
    '& video:not(:first-of-type)': {
      display: 'none !important',
    },
  },
}));

const ScanOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '20px',
  left: '20px',
  right: '20px',
  bottom: '20px',
  pointerEvents: 'none',
  zIndex: 15,
  borderRadius: '20px',
  // Show overlay when not actively scanning
  '&.scanning': {
    '&::before': {
      opacity: 0.3,
    },
    '&::after': {
      display: 'block',
    },
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '280px',
    height: '280px',
    border: `4px solid #1976d2`,
    borderRadius: '24px',
    boxShadow: `
      0 0 30px ${alpha('#1976d2', 0.6)},
      inset 0 0 20px ${alpha('#1976d2', 0.1)}
    `,
    background: `
      linear-gradient(135deg,
        ${alpha('#1976d2', 0.05)} 0%,
        transparent 50%,
        ${alpha('#1976d2', 0.05)} 100%
      )
    `,
    transition: 'all 0.3s ease',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '270px',
    height: '4px',
    background: `
      linear-gradient(90deg,
        transparent 0%,
        #1976d2 20%,
        #42a5f5 50%,
        #1976d2 80%,
        transparent 100%
      )
    `,
    animation: `${scanLine} 2.5s ease-in-out infinite`,
    boxShadow: `
      0 0 20px #1976d2,
      0 0 40px ${alpha('#1976d2', 0.5)}
    `,
    borderRadius: '2px',
  },
}));

const CornerFrame = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isScanning',
})(({ theme, isScanning }) => ({
  position: 'absolute',
  width: '50px',
  height: '50px',
  border: `5px solid #1976d2`,
  zIndex: 20,
  // Show corner frames with animation
  display: 'block',
  opacity: isScanning ? 0.8 : 1,
  animation: `${cornerGlow} 2s ease-in-out infinite`,
  transition: 'all 0.3s ease',
  '&.top-left': {
    top: 'calc(50% - 140px)',
    left: 'calc(50% - 140px)',
    borderRight: 'none',
    borderBottom: 'none',
    borderTopLeftRadius: '24px',
    boxShadow: `
      -5px -5px 15px ${alpha('#1976d2', 0.4)},
      0 0 20px ${alpha('#1976d2', 0.3)}
    `,
  },
  '&.top-right': {
    top: 'calc(50% - 140px)',
    right: 'calc(50% - 140px)',
    borderLeft: 'none',
    borderBottom: 'none',
    borderTopRightRadius: '24px',
    boxShadow: `
      5px -5px 15px ${alpha('#1976d2', 0.4)},
      0 0 20px ${alpha('#1976d2', 0.3)}
    `,
  },
  '&.bottom-left': {
    bottom: 'calc(50% - 140px)',
    left: 'calc(50% - 140px)',
    borderRight: 'none',
    borderTop: 'none',
    borderBottomLeftRadius: '24px',
    boxShadow: `
      -5px 5px 15px ${alpha('#1976d2', 0.4)},
      0 0 20px ${alpha('#1976d2', 0.3)}
    `,
  },
  '&.bottom-right': {
    bottom: 'calc(50% - 140px)',
    right: 'calc(50% - 140px)',
    borderLeft: 'none',
    borderTop: 'none',
    borderBottomRightRadius: '24px',
    boxShadow: `
      5px 5px 15px ${alpha('#1976d2', 0.4)},
      0 0 20px ${alpha('#1976d2', 0.3)}
    `,
  },
}));

const QrScanner = (props) => {
  const [scannedData, setScannedData] = useState('');
  const [cameraError, setCameraError] = useState(false);
  const [isScanning, setIsScanning] = useState(true);
  const [fileInputVisible, setFileInputVisible] = useState(false);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [scanningTips, setScanningTips] = useState(false);
  const [lastScanAttempt, setLastScanAttempt] = useState(0);
  const html5QrCodeRef = useRef(null);
  const theme = useTheme();

  const qrRegionId = "html5qr-code-full-region";

  const startScanner = async () => {
    // Stop any existing scanner first
    if (html5QrCodeRef.current) {
      try {
        if (html5QrCodeRef.current.getState() === Html5QrcodeScannerState.SCANNING) {
          await html5QrCodeRef.current.stop();
        }
      } catch (error) {
        // Scanner might not be initialized yet
      }
    }

    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 100));

    const html5QrCode = new Html5Qrcode(qrRegionId);
    html5QrCodeRef.current = html5QrCode;

    const config = {
      fps: 10,
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0,
      disableFlip: false,
      rememberLastUsedCamera: true,
      verbose: false
    };

    try {
      await html5QrCode.start(
        { facingMode: "environment" }, // Simplified camera constraint
        config,
        (decodedText) => {
          console.log(`✅ Successfully scanned QR Code: ${decodedText}`);
          setScannedData(decodedText);

          // Immediately stop scanner and pass data for faster response
          setIsScanning(false);

          // Stop scanner in background without waiting
          setTimeout(async () => {
            try {
              if (html5QrCode.getState() === Html5QrcodeScannerState.SCANNING) {
                await html5QrCode.stop();
              }
            } catch (error) {
              // Ignore errors during cleanup
            }
          }, 0);

          // Pass data immediately for faster navigation
          props.passData(decodedText);
        },
        (errorMessage) => {
          // Enhanced error handling - filter out common non-critical errors
          if (errorMessage &&
              !errorMessage.includes('NotFoundException') &&
              !errorMessage.includes('No MultiFormat Readers') &&
              !errorMessage.includes('IndexSizeError') &&
              !errorMessage.includes('source width is 0') &&
              !errorMessage.includes('getImageData') &&
              !errorMessage.includes('Canvas') &&
              !errorMessage.includes('parse error')) {
            console.warn('QR Scan Error:', errorMessage);
          }
          // Don't show errors for normal scanning operations
        }
      );
    } catch (err) {
      console.error('Camera start error:', err);
      setCameraError(true);
    }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];

    if (file) {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
      if (!validTypes.includes(file.type.toLowerCase())) {
        alert('Please select a valid image file (JPEG, PNG, GIF, BMP, or WebP)');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File is too large. Please select an image smaller than 10MB.');
        return;
      }

      const html5QrCode = new Html5Qrcode(qrRegionId);
      html5QrCodeRef.current = html5QrCode;

      // Read the QR from the uploaded file
      html5QrCode.scanFile(file, true).then((decodedText) => {
        console.log('✅ Successfully scanned QR Code from file:', decodedText);
        setScannedData(decodedText);
        props.passData(decodedText);
      }).catch((error) => {
        console.error('Error scanning file:', error);

        // Provide more specific error messages
        let errorMessage = 'Could not find a valid QR code in the image.';

        if (error.message && error.message.includes('NotFoundException')) {
          errorMessage = 'No QR code detected in the image. Please ensure the QR code is clearly visible and try again.';
        } else if (error.message && error.message.includes('No MultiFormat Readers')) {
          errorMessage = 'The image format is not supported or the QR code is not readable. Please try a different image.';
        } else if (error.message && error.message.includes('ChecksumException')) {
          errorMessage = 'The QR code appears to be damaged or corrupted. Please try a clearer image.';
        }

        alert(errorMessage + '\n\nTips:\n• Ensure good lighting when taking the photo\n• Keep the QR code flat and unfolded\n• Make sure the entire QR code is visible\n• Try taking the photo from a closer distance');
      });
    }
  };

  useEffect(() => {
    let isMounted = true;

    const initScanner = async () => {
      if (isScanning && isMounted) {
        try {
          await startScanner();
        } catch (error) {
          console.error('Failed to start scanner:', error);
          if (isMounted) {
            setCameraError(true);
          }
        }
      }
    };

    initScanner();

    return () => {
      isMounted = false;
      if (html5QrCodeRef.current) {
        try {
          if (html5QrCodeRef.current.getState() === Html5QrcodeScannerState.SCANNING) {
            html5QrCodeRef.current.stop().catch(() => {});
          }
        } catch (error) {
          // Scanner might not be initialized or already stopped
        }
      }
    };
  }, [isScanning]);

  const handleRetry = async () => {
    setScannedData('');
    setCameraError(false);

    // Stop any existing scanner first
    if (html5QrCodeRef.current) {
      try {
        if (html5QrCodeRef.current.getState() === Html5QrcodeScannerState.SCANNING) {
          await html5QrCodeRef.current.stop();
        }
      } catch (error) {
        // Ignore cleanup errors
      }
    }

    // Wait a moment before restarting
    setTimeout(() => {
      setIsScanning(true);
    }, 200);
  };

  const handleFileInputToggle = () => {
    setFileInputVisible(!fileInputVisible);
  };



  return (
    <ScannerContainer>
      {cameraError ? (
        <Fade in={true}>
          <Box sx={{
            p: 6,
            textAlign: 'center',
            background: `
              linear-gradient(135deg,
                ${alpha(theme.palette.error.main, 0.1)} 0%,
                ${alpha(theme.palette.error.main, 0.05)} 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            borderRadius: '20px',
            border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,
            boxShadow: `
              0 20px 60px ${alpha(theme.palette.error.main, 0.2)},
              inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
            `,
          }}>
            <Alert
              severity="error"
              sx={{
                mb: 4,
                borderRadius: '16px',
                background: `
                  linear-gradient(135deg,
                    ${alpha(theme.palette.error.main, 0.15)} 0%,
                    ${alpha(theme.palette.error.main, 0.08)} 100%
                  )
                `,
                border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,
                backdropFilter: 'blur(10px)',
                boxShadow: `0 8px 32px ${alpha(theme.palette.error.main, 0.2)}`,
                '& .MuiAlert-icon': {
                  fontSize: '2rem',
                },
                '& .MuiAlert-message': {
                  fontSize: '1rem',
                },
              }}
            >
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 700, mb: 2 }}>
                📷 Camera Access Required
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500, lineHeight: 1.6 }}>
                Unable to access the camera. Please allow camera permissions in your browser settings or try using a different device.
              </Typography>
            </Alert>

            <Box
              component="button"
              onClick={handleRetry}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                px: 6,
                py: 3,
                borderRadius: '16px',
                border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,
                background: `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`,
                color: 'white',
                fontWeight: 700,
                fontSize: '1.1rem',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                boxShadow: `0 8px 32px ${alpha(theme.palette.error.main, 0.3)}`,
                mx: 'auto',
                fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                '&:hover': {
                  transform: 'translateY(-4px) scale(1.05)',
                  boxShadow: `0 16px 50px ${alpha(theme.palette.error.main, 0.4)}`,
                  background: `linear-gradient(135deg, ${theme.palette.error.light} 0%, ${theme.palette.error.main} 100%)`,
                },
              }}
            >
              <Refresh sx={{ fontSize: 24 }} />
              Retry Camera Access
            </Box>
          </Box>
        </Fade>
      ) : isScanning ? (
        <Box>
          <ScannerFrame>
            <Box
              id={qrRegionId}
              sx={{
                width: '100%',
                minHeight: '300px',
                position: 'relative',
                '& video': {
                  borderRadius: '16px',
                  objectFit: 'cover',
                },
                '& canvas': {
                  borderRadius: '16px',
                }
              }}
            />
            <ScanOverlay className={isScanning ? 'scanning' : ''}>
              <CornerFrame className="top-left" isScanning={isScanning} />
              <CornerFrame className="top-right" isScanning={isScanning} />
              <CornerFrame className="bottom-left" isScanning={isScanning} />
              <CornerFrame className="bottom-right" isScanning={isScanning} />
            </ScanOverlay>
          </ScannerFrame>

          {/* Scanning Tips */}
          <Fade in={true}>
            <Alert
              severity="info"
              sx={{
                mt: 3,
                borderRadius: '16px',
                background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                '& .MuiAlert-icon': {
                  color: theme.palette.info.main,
                },
              }}
            >
              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                📱 Scanning Tips for Better Results:
              </Typography>
              <Typography variant="body2" component="div">
                • Hold your device steady and ensure good lighting<br/>
                • Keep the QR code flat and within the scanning area<br/>
                • Move closer or further away if the code isn't detected<br/>
                • Clean your camera lens for clearer scanning
              </Typography>
            </Alert>
          </Fade>

          <Box sx={{
            p: 4,
            background: `
              linear-gradient(135deg,
                ${alpha(theme.palette.background.paper, 0.9)} 0%,
                ${alpha(theme.palette.background.paper, 0.7)} 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
          }}>
            <Stack spacing={3} alignItems="center">
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 700,
                  color: '#1976d2',
                  textAlign: 'center',
                  fontSize: '1.1rem',
                  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                }}
              >
                🎯 Position QR Code in Center Frame
              </Typography>

              <Stack direction="row" spacing={3} justifyContent="center" alignItems="center">
                <Box
                  component="button"
                  onClick={handleFileInputToggle}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    px: 4,
                    py: 2.5,
                    borderRadius: '16px',
                    border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                    background: `
                      linear-gradient(135deg,
                        ${alpha(theme.palette.primary.main, 0.1)} 0%,
                        ${alpha(theme.palette.primary.main, 0.05)} 100%
                      )
                    `,
                    color: theme.palette.primary.main,
                    fontWeight: 600,
                    fontSize: '0.95rem',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    backdropFilter: 'blur(10px)',
                    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
                    '&:hover': {
                      transform: 'translateY(-2px) scale(1.02)',
                      boxShadow: `0 8px 30px ${alpha(theme.palette.primary.main, 0.25)}`,
                      background: `
                        linear-gradient(135deg,
                          ${alpha(theme.palette.primary.main, 0.15)} 0%,
                          ${alpha(theme.palette.primary.main, 0.08)} 100%
                        )
                      `,
                      border: `2px solid ${alpha(theme.palette.primary.main, 0.5)}`,
                    },
                  }}
                >
                  <Upload sx={{ fontSize: 20 }} />
                  Upload Image
                </Box>

                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    px: 4,
                    py: 2.5,
                    borderRadius: '16px',
                    border: `2px solid ${alpha('#1976d2', 0.3)}`,
                    background: `
                      linear-gradient(135deg,
                        ${alpha('#1976d2', 0.1)} 0%,
                        ${alpha('#1976d2', 0.05)} 100%
                      )
                    `,
                    color: '#1976d2',
                    fontWeight: 600,
                    fontSize: '0.95rem',
                    backdropFilter: 'blur(10px)',
                    boxShadow: `0 4px 20px ${alpha('#1976d2', 0.15)}`,
                    animation: `${pulse} 3s ease-in-out infinite`,
                  }}
                >
                  <CenterFocusStrong sx={{ fontSize: 20 }} />
                  Auto Focus Active
                </Box>
              </Stack>

              <Fade in={fileInputVisible}>
                <Box sx={{ width: '100%', maxWidth: 300 }}>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    style={{ display: 'none' }}
                    id="qr-file-input"
                  />
                  <label htmlFor="qr-file-input">
                    <Box
                      component="span"
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 2,
                        width: '100%',
                        px: 4,
                        py: 3,
                        borderRadius: '16px',
                        border: `2px dashed ${alpha(theme.palette.secondary.main, 0.4)}`,
                        background: `
                          linear-gradient(135deg,
                            ${alpha(theme.palette.secondary.main, 0.08)} 0%,
                            ${alpha(theme.palette.secondary.main, 0.03)} 100%
                          )
                        `,
                        color: theme.palette.secondary.main,
                        fontWeight: 600,
                        fontSize: '1rem',
                        cursor: 'pointer',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        backdropFilter: 'blur(10px)',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: `0 8px 30px ${alpha(theme.palette.secondary.main, 0.2)}`,
                          background: `
                            linear-gradient(135deg,
                              ${alpha(theme.palette.secondary.main, 0.12)} 0%,
                              ${alpha(theme.palette.secondary.main, 0.06)} 100%
                            )
                          `,
                          border: `2px dashed ${alpha(theme.palette.secondary.main, 0.6)}`,
                        },
                      }}
                    >
                      <Upload sx={{ fontSize: 24 }} />
                      Choose QR Code Image
                    </Box>
                  </label>
                </Box>
              </Fade>
            </Stack>
          </Box>
        </Box>
      ) : (
        <Fade in={true}>
          <Box sx={{
            p: 6,
            textAlign: 'center',
            background: `
              linear-gradient(135deg,
                ${alpha(theme.palette.success.main, 0.1)} 0%,
                ${alpha(theme.palette.success.main, 0.05)} 100%
              )
            `,
            backdropFilter: 'blur(20px)',
            borderRadius: '20px',
            border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,
            boxShadow: `
              0 20px 60px ${alpha(theme.palette.success.main, 0.2)},
              inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
            `,
          }}>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.light} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 3,
                boxShadow: `
                  0 10px 30px ${alpha(theme.palette.success.main, 0.3)},
                  inset 0 2px 0 ${alpha(theme.palette.common.white, 0.3)}
                `,
                animation: `${pulse} 2s ease-in-out infinite`,
              }}
            >
              <CenterFocusStrong sx={{ fontSize: 40, color: 'white' }} />
            </Box>

            <Typography
              variant="h4"
              gutterBottom
              sx={{
                fontWeight: 800,
                color: theme.palette.success.main,
                mb: 2,
                fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
              }}
            >
              ✅ QR Code Detected!
            </Typography>

            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                mb: 4,
                fontWeight: 500,
                fontSize: '1.1rem',
                background: `
                  linear-gradient(135deg,
                    ${alpha(theme.palette.background.paper, 0.8)} 0%,
                    ${alpha(theme.palette.background.paper, 0.6)} 100%
                  )
                `,
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                borderRadius: '12px',
                p: 3,
                wordBreak: 'break-all',
              }}
            >
              <strong style={{ color: theme.palette.success.main }}>Scanned Data:</strong><br />
              {scannedData}
            </Typography>

            <Box
              component="button"
              onClick={handleRetry}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                px: 6,
                py: 3,
                borderRadius: '16px',
                border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                color: 'white',
                fontWeight: 700,
                fontSize: '1.1rem',
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`,
                mx: 'auto',
                fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                '&:hover': {
                  transform: 'translateY(-4px) scale(1.05)',
                  boxShadow: `0 16px 50px ${alpha(theme.palette.primary.main, 0.4)}`,
                  background: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                },
              }}
            >
              <Refresh sx={{ fontSize: 24 }} />
              Scan Another QR Code
            </Box>
          </Box>
        </Fade>
      )}
    </ScannerContainer>
  );
};

export default QrScanner;
