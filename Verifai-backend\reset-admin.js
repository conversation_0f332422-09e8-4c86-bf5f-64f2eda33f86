const { Client } = require('pg');
const bcrypt = require('bcrypt');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

const saltRounds = 10;

async function resetAdminAccount() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        // Reset admin account password and first_login flag
        const username = 'admin';
        const password = 'admin123'; // Reset to original password

        console.log('Resetting admin account...');
        console.log(`Username: ${username}`);
        console.log(`Password: ${password}`);
        console.log(`Password length: ${password.length}`);
        console.log(`Setting first_login = true`);

        const hashedPassword = await bcrypt.hash(password, saltRounds);
        console.log(`Generated hash: ${hashedPassword}`);

        // Test the hash immediately
        const testResult = await bcrypt.compare(password, hashedPassword);
        console.log(`Hash test result: ${testResult}`);

        // Update the admin account
        await client.query(
            'UPDATE auth SET password = $1, first_login = $2 WHERE username = $3',
            [hashedPassword, true, username]
        );

        console.log('✅ Admin account reset successfully');

        // Verify the reset
        const verification = await client.query('SELECT username, role, first_login FROM auth WHERE username = $1', [username]);
        if (verification.rows.length > 0) {
            const account = verification.rows[0];
            console.log('\n🎉 Admin account verification:');
            console.log(`  Username: ${account.username}`);
            console.log(`  Role: ${account.role}`);
            console.log(`  First Login: ${account.first_login}`);
        }

        console.log('\n📝 You can now test with:');
        console.log(`  Username: ${username}`);
        console.log(`  Password: ${password}`);

    } catch (error) {
        console.error('❌ Error resetting admin account:', error.message);
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the script
resetAdminAccount();
