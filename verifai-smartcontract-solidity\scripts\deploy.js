const hre = require("hardhat");

const main = async () => {
  // Get the first signer (deployer)
  const [deployer] = await hre.ethers.getSigners();

  // Fetch the balance and format it
  const accountBalance = await deployer.getBalance();
  console.log("Deploying contracts with account:", deployer.address);
  console.log("Account balance:", hre.ethers.utils.formatEther(accountBalance), "ETH");  // Convert balance to ETH for readability

  // Get contract factory and deploy the contract
  const productContractFactory = await hre.ethers.getContractFactory("Verifai");
  const productContract = await productContractFactory.deploy();
  await productContract.deployed();

  console.log("Verifai contract deployed at:", productContract.address);
};

const runMain = async () => {
  try {
    await main();
    process.exit(0);
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

// Run the deployment
runMain();
