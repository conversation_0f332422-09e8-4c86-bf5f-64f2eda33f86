# 🚀 **VERIFAI PROJECT RESTART GUIDE**

## 📋 **Complete Step-by-Step Instructions**

Follow these steps in order to restart your VERIFAI project after shutting down your computer.

---

## 🔧 **STEP 1: Start Ganache Blockchain**

### **Option A: Ganache GUI (Recommended)**
1. **Open Ganache Desktop Application**
2. **Click "Quickstart"** or open existing workspace
3. **Verify Settings**:
   - Port: `7545`
   - Network ID: `1337`
   - Accounts: 10 accounts with 100 ETH each
4. **Keep Ganache Running** (don't close it)

### **Option B: Ganache CLI**
```bash
# Open Terminal 1
ganache-cli -p 7545 -i 1337
# Keep this terminal open
```

**✅ Success Indicator**: You should see 10 accounts listed with 100 ETH each

---

## 🔗 **STEP 2: Deploy Smart Contract**

```bash
# Open Terminal 2
cd verifai-smartcontract-solidity

# Compile and deploy contract
npx hardhat compile
node scripts/deploy-ganache.js

# OR if you have the deploy script:
npm run deploy:ganache
```

**✅ Success Indicator**: 
```
✅ Contract deployed successfully!
📍 Contract address: 0x[new-address]
✅ Registration and retrieval both tested successfully
```

**📝 Note**: The deployment script automatically updates the frontend with the new contract address.

---

## 🖥️ **STEP 3: Start Backend Server**

```bash
# Open Terminal 3
cd verifai-backend-nodejs
npm start

# Keep this terminal open
```

**✅ Success Indicator**:
```
Server is running on port 3000
Database connected successfully
```

---

## 🌐 **STEP 4: Start Frontend**

```bash
# Open Terminal 4
cd verifai-frontend-react
npm run vite

# Keep this terminal open
```

**✅ Success Indicator**:
```
Local:   http://localhost:5173/
Network: http://192.168.x.x:5173/
```

---

## 🔑 **STEP 5: Configure MetaMask**

### **Import Ganache Account**
1. **Open MetaMask**
2. **Click Account Icon** → "Import Account"
3. **Select "Private Key"**
4. **Copy Private Key from Ganache**:
   - In Ganache, click the key icon next to first account
   - Copy the private key
5. **Paste in MetaMask** and click "Import"

### **Add Ganache Network (if not exists)**
1. **MetaMask** → "Add Network" → "Add a network manually"
2. **Enter Details**:
   ```
   Network Name: Ganache Local
   RPC URL: http://127.0.0.1:7545
   Chain ID: 1337
   Currency Symbol: ETH
   ```
3. **Save** and **Switch to Ganache Local**

**✅ Success Indicator**: MetaMask shows ~100 ETH balance

---

## 🧪 **STEP 6: Test the Application**

### **Quick Test Checklist**
1. **Open Browser**: http://localhost:5173
2. **Login**: Use manufacturer account (testmanu/testpass123)
3. **Register Product**: Go to Add Product → Fill details → Submit
4. **Expected Result**: Registration completes in 3-8 seconds with QR code
5. **Test Scanner**: Scan the generated QR code
6. **Expected Result**: Shows "Authentic Product" page

---

## 🔄 **TERMINAL SUMMARY**

You should have **4 terminals running**:

```
Terminal 1: Ganache (if using CLI)
Terminal 2: Smart contract deployment (can close after deployment)
Terminal 3: Backend server (keep open)
Terminal 4: Frontend server (keep open)
```

---

## ⚠️ **TROUBLESHOOTING**

### **Issue**: "Contract not found" or registration fails
**Solution**:
```bash
cd verifai-smartcontract-solidity
node scripts/simple-test.js
# Should show successful registration and retrieval
```

### **Issue**: MetaMask shows 0 ETH
**Solution**:
1. Check Ganache is running on port 7545
2. Verify MetaMask network is "Ganache Local"
3. Re-import Ganache account private key

### **Issue**: Frontend shows connection errors
**Solution**:
1. Verify backend is running on port 3000
2. Check Ganache is running on port 7545
3. Restart frontend: `Ctrl+C` then `npm run vite`

### **Issue**: "Invalid contract address" when scanning
**Solution**:
1. Redeploy contract: `node scripts/deploy-ganache.js`
2. The script automatically updates frontend
3. Refresh browser page

### **Issue**: Registration hangs or fails
**Solution**:
1. Check MetaMask is connected to Ganache Local
2. Verify sufficient ETH balance (should be ~100 ETH)
3. Reset MetaMask account: Settings → Advanced → Reset Account

---

## 📊 **WORKING CONFIGURATION REFERENCE**

### **Current Working Setup**:
- **Contract Address**: Auto-generated on each deployment
- **Ganache URL**: http://127.0.0.1:7545
- **Chain ID**: 1337
- **Backend URL**: http://localhost:3000
- **Frontend URL**: http://localhost:5173

### **Test Accounts**:
- **Manufacturer**: testmanu / testpass123
- **Supplier**: testsupplier / testpass123
- **Retailer**: testretailer / testpass123
- **Admin**: testadmin / testpass123

---

## 🎯 **QUICK START COMMANDS**

For experienced users, here's the quick version:

```bash
# Terminal 1: Start Ganache GUI or CLI
ganache-cli -p 7545 -i 1337

# Terminal 2: Deploy contract
cd verifai-smartcontract-solidity && node scripts/deploy-ganache.js

# Terminal 3: Start backend
cd verifai-backend-nodejs && npm start

# Terminal 4: Start frontend
cd verifai-frontend-react && npm run vite

# Browser: http://localhost:5173
# MetaMask: Import Ganache account, switch to Ganache Local network
```

---

## ✅ **SUCCESS INDICATORS**

When everything is working correctly:
- ✅ Ganache shows 10 accounts with 100 ETH each
- ✅ Contract deployment shows successful test
- ✅ Backend shows "Server running on port 3000"
- ✅ Frontend shows "Local: http://localhost:5173"
- ✅ MetaMask shows ~100 ETH on Ganache Local
- ✅ Product registration completes in 3-8 seconds
- ✅ QR scanning shows authentic products

---

## 💾 **SAVE THIS GUIDE**

Save this file as `PROJECT_RESTART_GUIDE.md` in your project root for easy reference!

**Your VERIFAI project should now be fully operational!** 🎉
