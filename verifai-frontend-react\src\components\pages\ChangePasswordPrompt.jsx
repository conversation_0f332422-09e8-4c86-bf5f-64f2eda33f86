import { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Snackbar,
  Alert,
  InputAdornment,
  IconButton,
  LinearProgress,
  styled,
  useTheme,
  Container
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  LockReset,
  CheckCircle,
  Cancel,
  Person
} from '@mui/icons-material';
import { ThemeProvider } from '@mui/material/styles';
import createAppTheme from '../../theme';
import {
  FuturisticContainer,
  GlassCard,
  NeonButton,
  NeonTextField,
  GradientText,
  NeonAvatar
} from '../common/FuturisticComponents';
import ParticleField from '../common/ParticleField';

const PasswordStrengthIndicator = styled(Box)(({ strength, theme }) => ({
  height: 4,
  borderRadius: 2,
  backgroundColor: 
    strength === 0 ? theme.palette.error.main :
    strength === 1 ? theme.palette.warning.main :
    strength === 2 ? theme.palette.info.main :
    theme.palette.success.main,
  transition: 'all 0.3s ease',
  marginTop: theme.spacing(0.5),
  marginBottom: theme.spacing(1)
}));

const RequirementItem = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'valid'
})(({ valid, theme }) => ({
  display: 'flex',
  alignItems: 'center',
  color: valid ? '#2e7d32' : '#666666', // Strong green for valid, dark gray for invalid
  fontSize: '0.875rem',
  marginBottom: theme.spacing(0.5),
  fontWeight: 500,
  '& svg': {
    marginRight: theme.spacing(1),
    fontSize: '1rem',
    color: valid ? '#2e7d32' : '#d32f2f' // Green for valid, red for invalid
  }
}));

const ChangePasswordPrompt = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Get username from localStorage (stored during login)
  const [username, setUsername] = useState('');

  const [formData, setFormData] = useState({
    currentPassword: '',
    password: '',
    confirmPassword: ''
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [status, setStatus] = useState({
    error: '',
    success: false,
    loading: false
  });

  // Get username from localStorage on component mount
  useEffect(() => {
    const storedUsername = localStorage.getItem('first_time_user');
    if (storedUsername) {
      setUsername(storedUsername);
    } else {
      // If no username found, redirect to login
      navigate('/login');
    }
  }, [navigate]);

  const passwordStrength = () => {
    let strength = 0;
    if (formData.password.length >= 8) strength++;
    if (/[A-Z]/.test(formData.password)) strength++;
    if (/[0-9]/.test(formData.password)) strength++;
    if (/[^A-Za-z0-9]/.test(formData.password)) strength++;
    return Math.min(3, Math.floor(strength / 1.5));
  };

  const validatePassword = () => {
    const requirements = {
      length: formData.password.length >= 8,
      uppercase: /[A-Z]/.test(formData.password),
      number: /[0-9]/.test(formData.password),
      special: /[^A-Za-z0-9]/.test(formData.password)
    };
    return requirements;
  };

  const handlePasswordChange = async () => {
    setStatus({ ...status, loading: true, error: '' });

    // Validate all fields are filled
    if (!formData.currentPassword || !formData.password || !formData.confirmPassword) {
      setStatus({
        error: 'Please fill in all fields',
        success: false,
        loading: false
      });
      return;
    }

    // Validate new passwords match
    if (formData.password !== formData.confirmPassword) {
      setStatus({
        error: 'New passwords do not match',
        success: false,
        loading: false
      });
      return;
    }

    // Validate new password is different from current password
    if (formData.currentPassword === formData.password) {
      setStatus({
        error: 'New password must be different from current password',
        success: false,
        loading: false
      });
      return;
    }

    // Validate password strength
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(formData.password)) {
      setStatus({
        error: 'New password must contain at least 8 characters, one uppercase, one number, and one special character',
        success: false,
        loading: false
      });
      return;
    }

    try {
      // Change password with current password verification
      await axios.post('http://localhost:3000/changepsw', {
        username,
        currentPassword: formData.currentPassword,
        password: formData.password
      });

      localStorage.removeItem('first_time_user');
      setStatus({
        error: '',
        success: true,
        loading: false
      });
      setTimeout(() => navigate('/login'), 1500);
    } catch (err) {
      console.error('Password change error:', err);

      if (err.response?.status === 401) {
        setStatus({
          error: 'Current password is incorrect',
          success: false,
          loading: false
        });
      } else if (err.response?.data?.code === 'INVALID_CURRENT_PASSWORD') {
        setStatus({
          error: 'Current password is incorrect',
          success: false,
          loading: false
        });
      } else {
        setStatus({
          error: err.response?.data?.message || 'Error updating password. Please try again.',
          success: false,
          loading: false
        });
      }
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const requirements = validatePassword();

  return (
    <ThemeProvider theme={createAppTheme('light')}>
      <FuturisticContainer>
        <ParticleField density="medium" animated={true} />

        <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            minHeight: '100vh',
            justifyContent: 'center',
            py: 4
          }}>
            <GlassCard sx={{ maxWidth: 500, width: '100%', p: 5 }}>
              {/* Header Section */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <NeonAvatar sx={{
                  width: 80,
                  height: 80,
                  mb: 3,
                  mx: 'auto'
                }}>
                  <LockReset fontSize="large" />
                </NeonAvatar>

                <GradientText variant="h4" component="h1" sx={{ mb: 1 }}>
                  Change Your Password
                </GradientText>

                <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                  Please enter your current password and create a new secure password
                </Typography>

                {/* Show username */}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 3 }}>
                  <Person fontSize="small" color="primary" />
                  <Typography variant="body2" color="text.secondary">
                    Account: <strong>{username}</strong>
                  </Typography>
                </Box>
              </Box>

              {/* Password Form */}
              <Box sx={{ mb: 3 }}>
                <NeonTextField
                  name="currentPassword"
                  label="Current Password (assigned by admin)"
                  type={showCurrentPassword ? 'text' : 'password'}
                  fullWidth
                  margin="normal"
                  value={formData.currentPassword}
                  onChange={handleChange}
                  variant="outlined"
                  required
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            edge="end"
                            aria-label={showCurrentPassword ? "Hide password" : "Show password"}
                          >
                            {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }
                  }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <NeonTextField
                  name="password"
                  label="New Password"
                  type={showPassword ? 'text' : 'password'}
                  fullWidth
                  margin="normal"
                  value={formData.password}
                  onChange={handleChange}
                  variant="outlined"
                  required
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                            aria-label={showPassword ? "Hide password" : "Show password"}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }
                  }}
                />
                <PasswordStrengthIndicator strength={passwordStrength()} />
              </Box>

              <Box sx={{ mb: 3 }}>
                <NeonTextField
                  name="confirmPassword"
                  label="Confirm New Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  fullWidth
                  margin="normal"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  variant="outlined"
                  required
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                            aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                          >
                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }
                  }}
                />
              </Box>

              {/* Password Requirements */}
              <Box sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderRadius: 2,
                p: 3,
                mb: 3,
                border: `1px solid ${theme.palette.divider}`,
                backdropFilter: 'blur(10px)'
              }}>
                <Typography variant="subtitle2" sx={{
                  mb: 2,
                  fontWeight: 600,
                  color: theme.palette.text.primary
                }}>
                  Password Requirements:
                </Typography>
                <RequirementItem valid={requirements.length}>
                  {requirements.length ? <CheckCircle fontSize="small" /> : <Cancel fontSize="small" />}
                  At least 8 characters long
                </RequirementItem>
                <RequirementItem valid={requirements.uppercase}>
                  {requirements.uppercase ? <CheckCircle fontSize="small" /> : <Cancel fontSize="small" />}
                  At least one uppercase letter
                </RequirementItem>
                <RequirementItem valid={requirements.number}>
                  {requirements.number ? <CheckCircle fontSize="small" /> : <Cancel fontSize="small" />}
                  At least one number
                </RequirementItem>
                <RequirementItem valid={requirements.special}>
                  {requirements.special ? <CheckCircle fontSize="small" /> : <Cancel fontSize="small" />}
                  At least one special character
                </RequirementItem>
              </Box>

              {/* Inline Error Display */}
              {status.error && (
                <Alert
                  severity="error"
                  variant="outlined"
                  sx={{
                    mb: 3,
                    backgroundColor: 'rgba(211, 47, 47, 0.1)',
                    borderColor: '#d32f2f',
                    color: '#d32f2f',
                    fontWeight: 600,
                    '& .MuiAlert-message': {
                      color: '#d32f2f',
                      fontWeight: 600,
                      fontSize: '0.95rem'
                    },
                    '& .MuiAlert-icon': {
                      color: '#d32f2f'
                    }
                  }}
                >
                  {status.error}
                </Alert>
              )}

              {/* Success Message */}
              {status.success && (
                <Alert
                  severity="success"
                  variant="outlined"
                  sx={{
                    mb: 3,
                    backgroundColor: 'rgba(46, 125, 50, 0.1)',
                    borderColor: '#2e7d32',
                    color: '#2e7d32',
                    fontWeight: 600,
                    '& .MuiAlert-message': {
                      color: '#2e7d32',
                      fontWeight: 600,
                      fontSize: '0.95rem'
                    },
                    '& .MuiAlert-icon': {
                      color: '#2e7d32'
                    }
                  }}
                >
                  Password updated successfully! Redirecting to login...
                </Alert>
              )}

              {/* Submit Button */}
              <NeonButton
                variant="primary"
                fullWidth
                size="large"
                onClick={handlePasswordChange}
                disabled={status.loading}
                sx={{
                  py: 2,
                  fontSize: '1.1rem',
                  mb: 2
                }}
              >
                {status.loading ? 'Updating Password...' : 'Update Password'}
              </NeonButton>

              {status.loading && <LinearProgress sx={{ mt: 2 }} />}

              {/* Status Messages */}
              <Snackbar
                open={!!status.error || status.success}
                autoHideDuration={status.success ? 3000 : 6000}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                sx={{ mt: 6 }}
              >
                <Alert
                  severity={status.success ? 'success' : 'error'}
                  variant="filled"
                  sx={{
                    width: '100%',
                    color: '#ffffff',
                    fontWeight: 600,
                    fontSize: '0.95rem',
                    '& .MuiAlert-message': {
                      color: '#ffffff',
                      fontWeight: 600
                    },
                    backgroundColor: status.success
                      ? theme.palette.success.main
                      : theme.palette.error.main,
                    '&.MuiAlert-filledError': {
                      backgroundColor: '#d32f2f',
                      color: '#ffffff'
                    },
                    '&.MuiAlert-filledSuccess': {
                      backgroundColor: '#2e7d32',
                      color: '#ffffff'
                    }
                  }}
                >
                  {status.success ? 'Password updated successfully! Redirecting to login...' : status.error}
                </Alert>
              </Snackbar>
            </GlassCard>
          </Box>
        </Container>
      </FuturisticContainer>
    </ThemeProvider>
  );
};

export default ChangePasswordPrompt;