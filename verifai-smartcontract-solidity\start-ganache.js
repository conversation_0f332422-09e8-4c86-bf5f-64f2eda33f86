const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log("🚀 Starting Ganache with optimized settings for Verifai...\n");

// Ganache configuration optimized for development
const ganacheArgs = [
    '--host', '127.0.0.1',
    '--port', '7545',
    '--chainId', '1337',
    '--networkId', '1337',
    '--accounts', '10',
    '--defaultBalanceEther', '100',
    '--gasLimit', '6721975',
    '--gasPrice', '***********',
    '--mnemonic', 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about',
    '--deterministic',
    '--verbose'
];

console.log("📋 Ganache Configuration:");
console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
console.log("🌐 Host: 127.0.0.1");
console.log("🔌 Port: 7545");
console.log("🔗 Chain ID: 1337");
console.log("👥 Accounts: 10");
console.log("💰 Balance per account: 100 ETH");
console.log("⛽ Gas Limit: 6,721,975");
console.log("💸 Gas Price: 20 Gwei");
console.log("🎲 Deterministic: Yes");
console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");

// Start Ganache
const ganache = spawn('ganache', ganacheArgs, {
    stdio: 'inherit',
    shell: true
});

// Handle process events
ganache.on('spawn', () => {
    console.log("✅ Ganache started successfully!");
    console.log("🔗 RPC URL: http://127.0.0.1:7545");
    console.log("📊 You can view the blockchain at: http://127.0.0.1:7545");
    
    // Save Ganache info for other scripts
    const ganacheInfo = {
        host: '127.0.0.1',
        port: 7545,
        chainId: 1337,
        rpcUrl: 'http://127.0.0.1:7545',
        accounts: 10,
        balancePerAccount: '100 ETH',
        mnemonic: 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about',
        startTime: new Date().toISOString()
    };
    
    const infoPath = path.join(__dirname, 'ganache-info.json');
    fs.writeFileSync(infoPath, JSON.stringify(ganacheInfo, null, 2));
    
    console.log("\n📋 Quick Setup Guide:");
    console.log("1. Add network to MetaMask:");
    console.log("   - Network Name: Ganache Local");
    console.log("   - RPC URL: http://127.0.0.1:7545");
    console.log("   - Chain ID: 1337");
    console.log("   - Currency Symbol: ETH");
    console.log("\n2. Import account to MetaMask:");
    console.log("   - Private Key: 0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d");
    console.log("   - This account will have 100 ETH");
    console.log("\n3. Deploy contract:");
    console.log("   - Run: npm run deploy:ganache");
    console.log("   - Or: npx hardhat run scripts/deploy-ganache.js --network ganache");
    
    console.log("\n🎯 Ganache is ready! Press Ctrl+C to stop.");
});

ganache.on('error', (error) => {
    console.error("❌ Failed to start Ganache:", error.message);
    
    if (error.code === 'ENOENT') {
        console.error("\n💡 Solution: Install Ganache globally:");
        console.error("   npm install -g ganache");
        console.error("   or");
        console.error("   npm install -g ganache-cli");
    }
    
    process.exit(1);
});

ganache.on('close', (code) => {
    console.log(`\n🛑 Ganache stopped with exit code ${code}`);
    
    // Clean up info file
    const infoPath = path.join(__dirname, 'ganache-info.json');
    if (fs.existsSync(infoPath)) {
        fs.unlinkSync(infoPath);
    }
    
    process.exit(code);
});

// Handle script termination
process.on('SIGINT', () => {
    console.log('\n🛑 Stopping Ganache...');
    ganache.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Terminating Ganache...');
    ganache.kill('SIGTERM');
});
