import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Button,
  TextField,
  Card,
  Chip,
  Avatar,
  Typography,
  alpha,
  useTheme
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { useIconColors } from './EnhancedIcon';

// Smooth professional animations with enhanced easing
const smoothFloat = keyframes`
  0%, 100% {
    transform: translateY(0px) rotateX(0deg) scale(1);
    opacity: 1;
  }
  33% {
    transform: translateY(-8px) rotateX(2deg) scale(1.01);
    opacity: 0.95;
  }
  66% {
    transform: translateY(-12px) rotateX(3deg) scale(1.02);
    opacity: 0.98;
  }
`;

const elegantGlow = keyframes`
  0%, 100% {
    box-shadow:
      0 0 20px rgba(99, 102, 241, 0.2),
      0 0 40px rgba(99, 102, 241, 0.1),
      0 4px 20px rgba(99, 102, 241, 0.15);
    filter: brightness(1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(99, 102, 241, 0.4),
      0 0 60px rgba(99, 102, 241, 0.2),
      0 8px 30px rgba(99, 102, 241, 0.25);
    filter: brightness(1.05);
  }
`;

const fluidShimmer = keyframes`
  0% {
    background-position: -200% 0;
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200% 0;
    opacity: 0.8;
  }
`;

const breathingPulse = keyframes`
  0%, 100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  25% {
    opacity: 0.9;
    transform: scale(1.01) rotate(0.5deg);
    filter: brightness(1.02);
  }
  50% {
    opacity: 0.95;
    transform: scale(1.02) rotate(0deg);
    filter: brightness(1.05);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.01) rotate(-0.5deg);
    filter: brightness(1.02);
  }
`;

const subtleNeonPulse = keyframes`
  0%, 100% {
    box-shadow:
      0 0 5px currentColor,
      0 0 10px rgba(99, 102, 241, 0.3),
      0 0 15px rgba(99, 102, 241, 0.2);
    filter: brightness(1);
  }
  50% {
    box-shadow:
      0 0 10px currentColor,
      0 0 20px rgba(99, 102, 241, 0.5),
      0 0 30px rgba(99, 102, 241, 0.3);
    filter: brightness(1.1);
  }
`;

const gradientFlow = keyframes`
  0% {
    background-position: 0% 50%;
    transform: scale(1);
  }
  25% {
    background-position: 50% 25%;
    transform: scale(1.01);
  }
  50% {
    background-position: 100% 50%;
    transform: scale(1.02);
  }
  75% {
    background-position: 50% 75%;
    transform: scale(1.01);
  }
  100% {
    background-position: 0% 50%;
    transform: scale(1);
  }
`;

const morphingBorder = keyframes`
  0%, 100% {
    border-radius: 28px;
    transform: rotate(0deg);
  }
  25% {
    border-radius: 32px 24px 28px 32px;
    transform: rotate(0.5deg);
  }
  50% {
    border-radius: 24px 32px 24px 32px;
    transform: rotate(0deg);
  }
  75% {
    border-radius: 32px 28px 32px 24px;
    transform: rotate(-0.5deg);
  }
`;

// Additional smooth animations
const rippleEffect = keyframes`
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
`;

const smoothBounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -8px, 0) scale(1.02);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -4px, 0) scale(1.01);
  }
  90% {
    transform: translate3d(0, -1px, 0);
  }
`;

const textGlow = keyframes`
  0%, 100% {
    text-shadow:
      0 0 5px rgba(99, 102, 241, 0.5),
      0 0 10px rgba(99, 102, 241, 0.3),
      0 0 15px rgba(99, 102, 241, 0.2);
  }
  50% {
    text-shadow:
      0 0 10px rgba(99, 102, 241, 0.8),
      0 0 20px rgba(99, 102, 241, 0.5),
      0 0 30px rgba(99, 102, 241, 0.3);
  }
`;

const hoverLift = keyframes`
  0% {
    transform: translateY(0px) scale(1);
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.1);
  }
  100% {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(99, 102, 241, 0.2);
  }
`;

// Fix missing animation references
const float = smoothFloat;
const glow = elegantGlow;
const shimmer = fluidShimmer;
const pulse = breathingPulse;
const holographicShift = gradientFlow;

// Futuristic Container with advanced background
export const FuturisticContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `
    linear-gradient(135deg,
      ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#f8fafc'} 0%,
      ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#e2e8f0'} 50%,
      ${theme.palette.mode === 'dark' ? '#16213e' : '#cbd5e1'} 100%
    )
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 20%, ${alpha(theme.palette.primary.main, 0.15)} 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, ${alpha(theme.palette.secondary.main, 0.15)} 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, ${alpha(theme.palette.accent?.main || '#d946ef', 0.15)} 0%, transparent 50%),
      radial-gradient(circle at 60% 20%, ${alpha(theme.palette.cyber?.main || '#14b8a6', 0.1)} 0%, transparent 50%)
    `,
    pointerEvents: 'none',
    animation: `${holographicShift} 20s ease-in-out infinite`,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        ${alpha(theme.palette.primary.main, 0.03)} 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        ${alpha(theme.palette.secondary.main, 0.03)} 100px
      )
    `,
    pointerEvents: 'none',
  },
}));

// Next-generation Glass Card with holographic effects
export const GlassCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.95)} 0%,
    ${alpha(theme.palette.background.paper, 0.8)} 50%,
    ${alpha(theme.palette.background.paper, 0.9)} 100%
  )`,
  backdropFilter: 'blur(25px) saturate(180%)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  borderRadius: '28px',
  boxShadow: `
    0 12px 40px ${alpha(theme.palette.common.black, 0.15)},
    0 4px 16px ${alpha(theme.palette.primary.main, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.3)},
    inset 0 -1px 0 ${alpha(theme.palette.common.black, 0.1)}
  `,
  animation: `${float} 8s ease-in-out infinite`,
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '3px',
    background: `linear-gradient(90deg,
      ${theme.palette.primary.main} 0%,
      ${theme.palette.secondary.main} 25%,
      ${theme.palette.accent?.main || '#d946ef'} 50%,
      ${theme.palette.cyber?.main || '#14b8a6'} 75%,
      ${theme.palette.primary.main} 100%
    )`,
    backgroundSize: '200% 100%',
    animation: `${shimmer} 3s ease-in-out infinite`,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 30% 30%, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, ${alpha(theme.palette.secondary.main, 0.05)} 0%, transparent 50%)
    `,
    pointerEvents: 'none',
    animation: `${holographicShift} 15s ease-in-out infinite`,
  },
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    boxShadow: `
      0 25px 50px ${alpha(theme.palette.common.black, 0.2)},
      0 8px 32px ${alpha(theme.palette.primary.main, 0.2)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.4)}
    `,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.5)}`,
  },
}));

// Neon Button with advanced effects
export const NeonButton = styled(Button)(({ theme, variant = 'primary' }) => ({
  borderRadius: '16px',
  padding: '12px 32px',
  fontWeight: 700,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  background: variant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: variant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: variant === 'secondary' ? `2px solid ${alpha(theme.palette.primary.main, 0.3)}` : 'none',
  boxShadow: variant === 'primary'
    ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 15px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: variant === 'primary'
      ? `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.common.black, 0.15)}`,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0px)',
  },
}));

// Neon TextField with glow effects
export const NeonTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.8)} 0%,
      ${alpha(theme.palette.background.paper, 0.6)} 100%
    )`,
    backdropFilter: 'blur(10px)',
    borderRadius: '16px',
    transition: 'all 0.3s ease',
    '& fieldset': {
      border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
      borderRadius: '16px',
    },
    '&:hover fieldset': {
      border: `2px solid ${alpha(theme.palette.primary.main, 0.5)}`,
      boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.2)}`,
    },
    '&.Mui-focused fieldset': {
      border: `2px solid ${theme.palette.primary.main}`,
      boxShadow: `0 0 25px ${alpha(theme.palette.primary.main, 0.4)}`,
      animation: `${glow} 2s ease-in-out infinite`,
    },
  },
  '& .MuiInputLabel-root': {
    color: theme.palette.text.secondary,
    fontWeight: 600,
    '&.Mui-focused': {
      color: theme.palette.primary.main,
    },
  },
}));

// Cyber Button with advanced styling
export const CyberButton = styled(Button)(({ theme, variant = 'primary' }) => ({
  borderRadius: '16px',
  padding: '12px 32px',
  fontWeight: 700,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  background: variant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: variant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: variant === 'secondary' ? `2px solid ${alpha(theme.palette.primary.main, 0.3)}` : 'none',
  boxShadow: variant === 'primary'
    ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 15px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: variant === 'primary'
      ? `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.common.black, 0.15)}`,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0px)',
  },
}));

// Status Chip with dynamic colors
export const StatusChip = styled(Chip)(({ theme, status }) => ({
  borderRadius: '12px',
  fontWeight: 600,
  padding: '8px 4px',
  background: status === 'success'
    ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
    : status === 'error'
    ? `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`
    : status === 'warning'
    ? `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: theme.palette.common.white,
  boxShadow: `0 4px 15px ${alpha(
    status === 'success' ? theme.palette.success.main :
    status === 'error' ? theme.palette.error.main : 
    status === 'warning' ? theme.palette.warning.main : theme.palette.primary.main,
    0.3
  )}`,
  animation: status === 'processing' ? `${pulse} 2s ease-in-out infinite` : 'none',
}));

// Neon Avatar with glow
export const NeonAvatar = styled(Avatar)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.4)}`,
  border: `2px solid ${alpha(theme.palette.common.white, 0.3)}`,
  animation: `${glow} 3s ease-in-out infinite`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.1)',
    boxShadow: `0 12px 35px ${alpha(theme.palette.primary.main, 0.6)}`,
  },
}));

// Holographic Gradient Text component
export const GradientText = styled(Typography)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${theme.palette.primary.main} 0%,
    ${theme.palette.secondary.main} 25%,
    ${theme.palette.accent?.main || '#d946ef'} 50%,
    ${theme.palette.cyber?.main || '#14b8a6'} 75%,
    ${theme.palette.primary.main} 100%
  )`,
  backgroundSize: '200% 100%',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  fontWeight: 700,
  animation: `${shimmer} 4s ease-in-out infinite`,
}));

// Professional Tech Text component
export const TechText = styled(Typography)(({ theme }) => ({
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
  color: theme.palette.mode === 'dark' ? '#e2e8f0' : '#475569',
  fontWeight: 500,
  letterSpacing: '0.05em',
  position: 'relative',
  '&::before': {
    content: '">"',
    color: theme.palette.primary.main,
    marginRight: '8px',
    fontWeight: 700,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: '1px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, transparent 100%)`,
    opacity: 0.6,
  },
}));

// Professional Gradient Title component
export const ProfessionalTitle = styled(Typography)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${theme.palette.primary.main} 0%,
    ${theme.palette.secondary.main} 50%,
    ${theme.palette.primary.dark} 100%
  )`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  fontWeight: 700,
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
  letterSpacing: '-0.02em',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '60px',
    height: '3px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    borderRadius: '2px',
  },
}));

// Holographic Card component
export const HolographicCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(30px) saturate(200%)',
  border: `2px solid transparent`,
  borderRadius: '32px',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(45deg,
      ${theme.palette.primary.main},
      ${theme.palette.secondary.main},
      ${theme.palette.accent?.main || '#d946ef'},
      ${theme.palette.cyber?.main || '#14b8a6'},
      ${theme.palette.primary.main}
    )`,
    backgroundSize: '400% 400%',
    animation: `${holographicShift} 8s ease-in-out infinite`,
    zIndex: -1,
    margin: '-2px',
    borderRadius: 'inherit',
  },
  '&:hover': {
    transform: 'translateY(-8px) rotateX(5deg)',
    boxShadow: `
      0 20px 40px ${alpha(theme.palette.common.black, 0.2)},
      0 0 60px ${alpha(theme.palette.primary.main, 0.3)}
    `,
  },
}));

// Professional Status Indicator
export const StatusIndicator = styled(Box)(({ theme, status = 'active' }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  padding: '8px 16px',
  borderRadius: '12px',
  fontSize: '0.875rem',
  fontWeight: 600,
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
  background: status === 'active'
    ? `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.dark, 0.05)} 100%)`
    : status === 'processing'
    ? `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)} 0%, ${alpha(theme.palette.warning.dark, 0.05)} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.dark, 0.05)} 100%)`,
  border: `1px solid ${
    status === 'active'
      ? alpha(theme.palette.success.main, 0.3)
      : status === 'processing'
      ? alpha(theme.palette.warning.main, 0.3)
      : alpha(theme.palette.primary.main, 0.3)
  }`,
  color: status === 'active'
    ? theme.palette.success.main
    : status === 'processing'
    ? theme.palette.warning.main
    : theme.palette.primary.main,
  '&::before': {
    content: '""',
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    marginRight: '8px',
    background: status === 'active'
      ? theme.palette.success.main
      : status === 'processing'
      ? theme.palette.warning.main
      : theme.palette.primary.main,
    animation: status === 'processing' ? `${pulse} 2s ease-in-out infinite` : 'none',
  },
}));

// Professional Button with subtle effects
export const ProfessionalButton = styled(Button)(({ theme, variant = 'primary' }) => ({
  borderRadius: '12px',
  padding: '12px 32px',
  fontWeight: 600,
  fontSize: '1rem',
  textTransform: 'none',
  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
  position: 'relative',
  overflow: 'hidden',
  background: variant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
    : 'transparent',
  color: variant === 'primary' ? '#ffffff' : theme.palette.primary.main,
  border: variant === 'secondary'
    ? `2px solid ${theme.palette.primary.main}`
    : 'none',
  boxShadow: variant === 'primary'
    ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`
    : 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha('#ffffff', 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: variant === 'primary'
      ? `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 4px 16px ${alpha(theme.palette.primary.main, 0.2)}`,
    background: variant === 'secondary'
      ? alpha(theme.palette.primary.main, 0.05)
      : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0px)',
  },
}));
