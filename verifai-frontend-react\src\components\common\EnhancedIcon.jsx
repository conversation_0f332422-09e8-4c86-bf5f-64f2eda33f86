import React from 'react';
import { styled, alpha, useTheme } from '@mui/material/styles';
import { Box } from '@mui/material';

// Global Icon Enhancement System for Maximum Visibility and Contrast
const getIconContrastColor = (theme, variant = 'default', context = 'general') => {
  const isDark = theme.palette.mode === 'dark';
  
  // Context-specific color calculations for different backgrounds
  const contextColors = {
    general: {
      default: isDark ? alpha('#ffffff', 0.87) : alpha('#000000', 0.87),
      primary: isDark ? theme.palette.primary.light : theme.palette.primary.main,
      secondary: isDark ? theme.palette.secondary.light : theme.palette.secondary.main,
      success: isDark ? theme.palette.success.light : theme.palette.success.main,
      warning: isDark ? theme.palette.warning.light : theme.palette.warning.main,
      error: isDark ? theme.palette.error.light : theme.palette.error.main,
      muted: isDark ? alpha('#ffffff', 0.6) : alpha('#000000', 0.6),
    },
    glassmorphism: {
      default: isDark ? alpha('#ffffff', 0.95) : alpha('#000000', 0.95),
      primary: isDark ? theme.palette.primary.light : theme.palette.primary.dark,
      secondary: isDark ? theme.palette.secondary.light : theme.palette.secondary.dark,
      success: isDark ? theme.palette.success.light : theme.palette.success.dark,
      warning: isDark ? theme.palette.warning.light : theme.palette.warning.dark,
      error: isDark ? theme.palette.error.light : theme.palette.error.dark,
      muted: isDark ? alpha('#ffffff', 0.8) : alpha('#000000', 0.8),
    },
    navigation: {
      default: isDark ? alpha('#ffffff', 0.85) : alpha('#000000', 0.85),
      primary: theme.palette.primary.main,
      secondary: theme.palette.secondary.main,
      success: theme.palette.success.main,
      warning: theme.palette.warning.main,
      error: theme.palette.error.main,
      muted: isDark ? alpha('#ffffff', 0.55) : alpha('#000000', 0.55),
    },
    futuristic: {
      default: isDark ? alpha('#ffffff', 0.9) : alpha('#000000', 0.9),
      primary: isDark ? theme.palette.primary.light : theme.palette.primary.main,
      secondary: isDark ? theme.palette.secondary.light : theme.palette.secondary.main,
      success: isDark ? theme.palette.success.light : theme.palette.success.main,
      warning: isDark ? theme.palette.warning.light : theme.palette.warning.main,
      error: isDark ? theme.palette.error.light : theme.palette.error.main,
      muted: isDark ? alpha('#ffffff', 0.7) : alpha('#000000', 0.7),
    }
  };
  
  return contextColors[context]?.[variant] || contextColors.general[variant] || contextColors.general.default;
};

const getIconShadow = (theme, intensity = 'medium', context = 'general') => {
  const isDark = theme.palette.mode === 'dark';
  
  const shadows = {
    none: 'none',
    light: isDark 
      ? `0 1px 2px ${alpha('#000000', 0.4)}, 0 0 4px ${alpha('#ffffff', 0.1)}`
      : `0 1px 1px ${alpha('#ffffff', 0.9)}, 0 0 2px ${alpha('#000000', 0.1)}`,
    medium: isDark 
      ? `0 1px 3px ${alpha('#000000', 0.6)}, 0 0 6px ${alpha('#ffffff', 0.15)}`
      : `0 1px 2px ${alpha('#ffffff', 1)}, 0 0 4px ${alpha('#000000', 0.15)}`,
    strong: isDark 
      ? `0 2px 4px ${alpha('#000000', 0.8)}, 0 0 8px ${alpha('#ffffff', 0.2)}`
      : `0 2px 3px ${alpha('#ffffff', 1)}, 0 0 6px ${alpha('#000000', 0.2)}`,
    glow: isDark
      ? `0 0 8px ${alpha(theme.palette.primary.main, 0.4)}, 0 0 16px ${alpha(theme.palette.primary.main, 0.2)}`
      : `0 0 6px ${alpha(theme.palette.primary.main, 0.3)}, 0 0 12px ${alpha(theme.palette.primary.main, 0.1)}`,
  };
  
  return shadows[intensity] || shadows.medium;
};

// Enhanced Icon Container Component
const EnhancedIconContainer = styled(Box)(({ 
  theme, 
  variant = 'default', 
  size = 'medium', 
  context = 'general',
  intensity = 'medium',
  interactive = false,
  disabled = false
}) => {
  const sizeMap = {
    small: '1rem',
    medium: '1.25rem',
    large: '1.5rem',
    xlarge: '2rem',
  };

  return {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: disabled 
      ? alpha(getIconContrastColor(theme, 'muted', context), 0.5)
      : getIconContrastColor(theme, variant, context),
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    
    '& .MuiSvgIcon-root': {
      fontSize: sizeMap[size] || sizeMap.medium,
      filter: disabled
        ? `brightness(0.7) contrast(0.8) drop-shadow(${getIconShadow(theme, 'light', context)})`
        : `brightness(1.1) contrast(1.1) drop-shadow(${getIconShadow(theme, intensity, context)})`,
      transition: 'inherit',
    },
    
    ...(interactive && !disabled && {
      cursor: 'pointer',
      borderRadius: '8px',
      padding: '4px',
      
      '&:hover': {
        color: getIconContrastColor(theme, variant === 'default' ? 'primary' : variant, context),
        backgroundColor: alpha(getIconContrastColor(theme, 'primary', context), 0.08),
        transform: 'scale(1.05)',
        
        '& .MuiSvgIcon-root': {
          filter: `brightness(1.2) contrast(1.2) drop-shadow(${getIconShadow(theme, 'strong', context)})`,
        },
      },
      
      '&:active': {
        transform: 'scale(0.98)',
      },
    }),
  };
});

// Main Enhanced Icon Component
const EnhancedIcon = ({ 
  children, 
  variant = 'default', 
  size = 'medium', 
  context = 'general',
  intensity = 'medium',
  interactive = false,
  disabled = false,
  className,
  onClick,
  ...props 
}) => {
  return (
    <EnhancedIconContainer
      variant={variant}
      size={size}
      context={context}
      intensity={intensity}
      interactive={interactive}
      disabled={disabled}
      className={className}
      onClick={onClick}
      {...props}
    >
      {children}
    </EnhancedIconContainer>
  );
};

// Specialized Icon Components for different contexts
export const GlassmorphismIcon = (props) => (
  <EnhancedIcon context="glassmorphism" intensity="strong" {...props} />
);

export const NavigationIcon = (props) => (
  <EnhancedIcon context="navigation" intensity="medium" interactive {...props} />
);

export const FuturisticIcon = (props) => (
  <EnhancedIcon context="futuristic" intensity="glow" {...props} />
);

export const InteractiveIcon = (props) => (
  <EnhancedIcon interactive intensity="medium" {...props} />
);

// Hook for getting icon colors programmatically
export const useIconColors = (context = 'general') => {
  const theme = useTheme();
  
  return {
    getColor: (variant = 'default') => getIconContrastColor(theme, variant, context),
    getShadow: (intensity = 'medium') => getIconShadow(theme, intensity, context),
    theme,
  };
};

export default EnhancedIcon;
