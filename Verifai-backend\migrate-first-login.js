const { Client } = require('pg');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

async function runMigration() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        // Check if first_login column already exists
        const checkColumnQuery = `
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'auth' 
            AND table_schema = 'public' 
            AND column_name = 'first_login'
        `;
        
        const columnCheck = await client.query(checkColumnQuery);
        
        if (columnCheck.rows.length > 0) {
            console.log('✅ first_login column already exists');
            return;
        }

        console.log('Adding first_login column to auth table...');

        // Add the first_login column with default value true
        await client.query(`
            ALTER TABLE public.auth 
            ADD COLUMN first_login boolean DEFAULT true NOT NULL
        `);

        console.log('✅ Successfully added first_login column');

        // Set existing accounts to first_login = false (assuming they've already logged in)
        const updateResult = await client.query(`
            UPDATE public.auth 
            SET first_login = false 
            WHERE first_login IS NULL
        `);

        console.log(`✅ Updated ${updateResult.rowCount} existing accounts`);

        // Add comment for documentation
        await client.query(`
            COMMENT ON COLUMN public.auth.first_login IS 'Flag to track if user needs to change password on first login'
        `);

        console.log('✅ Added column comment');

        // Verify the migration
        const verifyQuery = `
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'auth' AND table_schema = 'public'
            ORDER BY ordinal_position
        `;
        
        const verification = await client.query(verifyQuery);
        
        console.log('\n📋 Current auth table schema:');
        verification.rows.forEach(row => {
            console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
        });

        console.log('\n🎉 Migration completed successfully!');

    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Make sure PostgreSQL is running and the connection details are correct');
        } else if (error.code === '42P01') {
            console.log('\n💡 The auth table does not exist. Please create the database tables first');
        }
        
        process.exit(1);
    } finally {
        await client.end();
        console.log('Database connection closed');
    }
}

// Run the migration
runMigration();
