import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  IconButton,
  Stack,
  alpha,
  useTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  TrendingUp,
  Security,
  Verified,
  Analytics,
  Speed,
  Shield,
  Timeline,
  Assessment
} from '@mui/icons-material';
import { 
  FuturisticContainer, 
  GlassCard, 
  GradientText,
  StatusChip
} from './FuturisticComponents';
import ParticleField from './ParticleField';

// Enhanced Dashboard Components
const DashboardCard = styled(GlassCard)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '3px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  borderRadius: '20px',
  padding: theme.spacing(3),
  height: '100%',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.4)}`,
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '100px',
    height: '100px',
    background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 70%)`,
    borderRadius: '50%',
    transform: 'translate(30px, -30px)',
  },
}));

const IconContainer = styled(Box)(({ theme, color = 'primary' }) => {
  // Enhanced color mapping for better visibility
  const colorMap = {
    primary: {
      main: theme.palette.primary.main,
      dark: theme.palette.primary.dark,
      light: theme.palette.primary.light,
    },
    secondary: {
      main: theme.palette.secondary.main,
      dark: theme.palette.secondary.dark,
      light: theme.palette.secondary.light,
    },
    success: {
      main: theme.palette.success.main,
      dark: theme.palette.success.dark,
      light: theme.palette.success.light,
    },
    cyber: {
      main: theme.palette.cyber?.main || '#14b8a6',
      dark: theme.palette.cyber?.dark || '#0f766e',
      light: theme.palette.cyber?.light || '#5eead4',
    },
    warning: {
      main: theme.palette.warning.main,
      dark: theme.palette.warning.dark,
      light: theme.palette.warning.light,
    },
    error: {
      main: theme.palette.error.main,
      dark: theme.palette.error.dark,
      light: theme.palette.error.light,
    },
  };

  const selectedColor = colorMap[color] || colorMap.primary;

  return {
    width: 60,
    height: 60,
    borderRadius: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: `linear-gradient(135deg, ${selectedColor.main} 0%, ${selectedColor.dark} 100%)`,
    boxShadow: `
      0 8px 25px ${alpha(selectedColor.main, 0.4)},
      0 4px 12px ${alpha(selectedColor.main, 0.2)},
      inset 0 1px 0 ${alpha('#ffffff', 0.3)}
    `,
    marginBottom: theme.spacing(2),
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '& .MuiSvgIcon-root': {
      fontSize: '1.75rem',
      color: '#ffffff',
      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))',
      zIndex: 2,
      position: 'relative',
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `radial-gradient(circle at 30% 30%, ${alpha(selectedColor.light, 0.3)} 0%, transparent 60%)`,
      zIndex: 1,
    },
    '&:hover': {
      transform: 'translateY(-2px) scale(1.05)',
      boxShadow: `
        0 12px 35px ${alpha(selectedColor.main, 0.5)},
        0 6px 16px ${alpha(selectedColor.main, 0.3)},
        inset 0 1px 0 ${alpha('#ffffff', 0.4)}
      `,
    },
  };
});

const StatsGrid = styled(Grid)(({ theme }) => ({
  '& .MuiGrid-item': {
    display: 'flex',
  },
}));

// Dashboard Layout Component
const DashboardLayout = ({ 
  title, 
  subtitle, 
  userRole, 
  stats = [], 
  children,
  actions = null 
}) => {
  const theme = useTheme();

  const defaultStats = [
    {
      title: 'Total Products',
      value: '1,234',
      change: '+12%',
      icon: <Assessment />,
      color: 'primary',
      trend: 'up'
    },
    {
      title: 'Verified Items',
      value: '1,180',
      change: '+8%',
      icon: <Verified />,
      color: 'success',
      trend: 'up'
    },
    {
      title: 'Security Score',
      value: '98.5%',
      change: '+2%',
      icon: <Security />,
      color: 'cyber',
      trend: 'up'
    },
    {
      title: 'Active Users',
      value: '456',
      change: '+15%',
      icon: <TrendingUp />,
      color: 'secondary',
      trend: 'up'
    }
  ];

  const displayStats = stats.length > 0 ? stats : defaultStats;

  return (
    <FuturisticContainer>
      <ParticleField density="low" animated={true} />
      
      <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 2 }}>
        {/* Header Section */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-start',
            mb: 2 
          }}>
            <Box>
              <GradientText variant="h3" component="h1" sx={{ mb: 1 }}>
                {title}
              </GradientText>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                {subtitle}
              </Typography>
              <StatusChip 
                label={userRole} 
                status="success" 
                icon={<Shield />}
              />
            </Box>
            
            {actions && (
              <Box sx={{ display: 'flex', gap: 2 }}>
                {actions}
              </Box>
            )}
          </Box>
        </Box>

        {/* Stats Grid */}
        <StatsGrid container spacing={3} sx={{ mb: 4 }}>
          {displayStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <MetricCard>
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  <IconContainer color={stat.color}>
                    {stat.icon}
                  </IconContainer>
                  
                  <Typography variant="h4" fontWeight={700} sx={{ mb: 1 }}>
                    {stat.value}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {stat.title}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={stat.change}
                      size="small"
                      color={stat.trend === 'up' ? 'success' : 'error'}
                      sx={{
                        fontWeight: 600,
                        fontSize: '0.75rem',
                      }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      vs last month
                    </Typography>
                  </Box>
                </Box>
              </MetricCard>
            </Grid>
          ))}
        </StatsGrid>

        {/* Main Content */}
        <Box sx={{ mb: 4 }}>
          {children}
        </Box>
      </Container>
    </FuturisticContainer>
  );
};

export default DashboardLayout;

// Quick Stats Component
export const QuickStats = ({ stats }) => {
  return (
    <Grid container spacing={2}>
      {stats.map((stat, index) => (
        <Grid item xs={6} sm={3} key={index}>
          <DashboardCard sx={{ p: 2, textAlign: 'center' }}>
            <IconContainer color={stat.color} sx={{ mx: 'auto', mb: 1, width: 40, height: 40 }}>
              {stat.icon}
            </IconContainer>
            <Typography variant="h6" fontWeight={600}>
              {stat.value}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {stat.label}
            </Typography>
          </DashboardCard>
        </Grid>
      ))}
    </Grid>
  );
};

// Action Card Component
export const ActionCard = ({ title, description, icon, action, color = 'primary' }) => {
  const theme = useTheme();
  
  return (
    <DashboardCard sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
        <IconContainer color={color} sx={{ width: 50, height: 50 }}>
          {icon}
        </IconContainer>
        
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h6" fontWeight={600} sx={{ mb: 1 }}>
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {description}
          </Typography>
          {action}
        </Box>
      </Box>
    </DashboardCard>
  );
};
