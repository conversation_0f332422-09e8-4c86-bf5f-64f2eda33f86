const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function debugUsernameResponse() {
    console.log('🔍 Debugging username response from backend...\n');

    try {
        // Test login with testmanu
        console.log('1️⃣ Testing login response for testmanu...');
        const loginResponse = await axios.post(`${BASE_URL}/auth`, {
            username: 'testmanu',
            password: 'testpass123'
        });

        console.log('📋 LOGIN RESPONSE:');
        console.log('  Full response:', JSON.stringify(loginResponse.data, null, 2));
        
        if (loginResponse.data.requirePasswordChange) {
            console.log('\n✅ First-time login detected');
            console.log(`  Username returned: "${loginResponse.data.username}"`);
            console.log(`  Username type: ${typeof loginResponse.data.username}`);
            console.log(`  Username length: ${loginResponse.data.username?.length}`);
            console.log(`  Username chars: ${loginResponse.data.username?.split('').map(c => `'${c}'`).join(', ')}`);
        }

        // Test with different case variations
        console.log('\n2️⃣ Testing case variations...');
        const testCases = ['testmanu', 'testManu', 'TestManu', 'TESTMANU'];
        
        for (const testCase of testCases) {
            try {
                console.log(`\n  Testing: "${testCase}"`);
                const response = await axios.post(`${BASE_URL}/auth`, {
                    username: testCase,
                    password: 'testpass123'
                });
                
                if (response.data.requirePasswordChange) {
                    console.log(`    ✅ Success - Username returned: "${response.data.username}"`);
                } else if (response.data.accessToken) {
                    console.log(`    ✅ Success - User: "${response.data.user?.username}"`);
                } else {
                    console.log(`    ❌ Failed - ${response.data.message}`);
                }
            } catch (error) {
                console.log(`    ❌ Failed - ${error.response?.data?.message || error.message}`);
            }
        }

        // Test profile endpoint
        console.log('\n3️⃣ Testing profile endpoint...');
        try {
            const profileResponse = await axios.get(`${BASE_URL}/profile/testmanu`);
            console.log('📋 PROFILE RESPONSE:');
            console.log('  Full response:', JSON.stringify(profileResponse.data, null, 2));
            
            if (profileResponse.data && profileResponse.data.length > 0) {
                const profile = profileResponse.data[0];
                console.log(`  Profile username: "${profile.username}"`);
                console.log(`  Profile name: "${profile.name}"`);
                console.log(`  Profile role: "${profile.role}"`);
            }
        } catch (error) {
            console.log(`  ❌ Profile request failed: ${error.response?.data?.message || error.message}`);
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('   Response:', error.response.data);
        }
    }
}

// Run the debug
debugUsernameResponse();
