const axios = require('axios');

async function debugLogin() {
    try {
        console.log('🔍 Testing login endpoint directly...');
        
        const response = await axios.post('http://localhost:3000/auth', {
            username: 'admin',
            password: 'admin123'
        });
        
        console.log('✅ Login successful!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ Login failed');
        console.log('Status:', error.response?.status);
        console.log('Error:', error.response?.data);
        console.log('Full error:', error.message);
    }
}

debugLogin();
