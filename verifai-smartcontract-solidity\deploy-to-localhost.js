const { ethers } = require("hardhat");

async function main() {
    console.log("🚀 Deploying Verifai contract to localhost...");

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("Deploying contracts with account:", deployer.address);

    // Get account balance
    const balance = await deployer.getBalance();
    console.log("Account balance:", ethers.utils.formatEther(balance), "ETH");

    // Get the contract factory
    const Verifai = await ethers.getContractFactory("Verifai");
    
    // Deploy the contract
    console.log("Deploying contract...");
    const verifai = await Verifai.deploy();
    
    // Wait for deployment to complete
    await verifai.deployed();
    
    console.log("✅ Verifai contract deployed at:", verifai.address);
    
    // Test the contract
    console.log("\n🧪 Testing contract...");
    try {
        // Try to register a test product
        const tx = await verifai.registerProduct(
            "Test Product",
            "Test Brand",
            "TEST123",
            "Test Description",
            "test.jpg",
            "Test Manufacturer",
            "0.0000, 0.0000",
            Math.floor(Date.now() / 1000).toString()
        );
        
        console.log("Test registration transaction:", tx.hash);
        await tx.wait();
        console.log("✅ Test registration successful!");
        
        // Try to get the product
        const product = await verifai.getProduct("TEST123");
        console.log("✅ Product retrieval successful!");
        console.log("Product name:", product[0]);
        
    } catch (error) {
        console.log("⚠️ Contract test failed:", error.message);
    }
    
    console.log("\n📋 Deployment Summary:");
    console.log("Contract Address:", verifai.address);
    console.log("Network: localhost (http://127.0.0.1:8545)");
    console.log("Chain ID: 1337");
    console.log("Deployer:", deployer.address);
    
    console.log("\n🔧 Next Steps:");
    console.log("1. Update your frontend CONTRACT_ADDRESS to:", verifai.address);
    console.log("2. Make sure MetaMask is connected to localhost:8545");
    console.log("3. Import one of the test accounts into MetaMask");
    console.log("4. Try registering a product from the frontend");
    
    return verifai.address;
}

main()
    .then((address) => {
        console.log(`\n🎉 Deployment completed successfully!`);
        console.log(`Contract address: ${address}`);
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    });
