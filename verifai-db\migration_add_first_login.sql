-- Migration script to add first_login column to existing auth table
-- Run this script if you have an existing database without the first_login column

-- Add the first_login column with default value true
ALTER TABLE public.auth 
ADD COLUMN IF NOT EXISTS first_login boolean DEFAULT true NOT NULL;

-- Set existing accounts to first_login = false (assuming they've already logged in)
-- You can modify this based on your specific needs
UPDATE public.auth 
SET first_login = false 
WHERE first_login IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.auth.first_login IS 'Flag to track if user needs to change password on first login';

-- Verify the migration
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'auth' AND table_schema = 'public'
ORDER BY ordinal_position;
