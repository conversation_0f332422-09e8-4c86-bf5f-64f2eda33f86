<!DOCTYPE html>
<html>
<head>
    <title>QR Code Scanner Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px; 
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-color: #17a2b8;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }
        button { 
            padding: 12px 24px; 
            margin: 8px; 
            cursor: pointer; 
            border: none;
            border-radius: 6px;
            background: #007bff;
            color: white;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        #reader {
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 20px;
        }
        .test-qr {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .tips {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 QR Code Scanner Diagnostic Tool</h1>
        <p>This tool helps diagnose QR scanning issues and test different scenarios.</p>
        
        <div id="status"></div>
        
        <div class="tips">
            <h3>📋 Before Testing:</h3>
            <ul>
                <li>Ensure your camera is working and permissions are granted</li>
                <li>Test with the sample QR code below first</li>
                <li>Try both camera scanning and file upload</li>
                <li>Check browser console for detailed error messages</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="startCameraScanning()" id="startBtn">📷 Start Camera Scanning</button>
            <button onclick="stopScanning()" id="stopBtn" disabled>⏹️ Stop Scanning</button>
            <button onclick="testFileUpload()" id="fileBtn">📁 Test File Upload</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>

        <div id="reader"></div>

        <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(event)">

        <div class="test-qr">
            <h3>📱 Test QR Code</h3>
            <p>Use this QR code to test scanning:</p>
            <div style="background: white; padding: 20px; display: inline-block; border-radius: 8px;">
                <div id="test-qr-code"></div>
            </div>
            <p><small>Contains: "0x2fcc261bB32262a150E4905F6d550D4FF05bC582,TEST123"</small></p>
        </div>
    </div>

    <script>
        let html5QrCode = null;
        let isScanning = false;

        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            statusDiv.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
            
            // Auto-scroll to latest log
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function clearLogs() {
            document.getElementById('status').innerHTML = '';
            log('Logs cleared', 'info');
        }

        function updateButtons() {
            document.getElementById('startBtn').disabled = isScanning;
            document.getElementById('stopBtn').disabled = !isScanning;
        }

        async function startCameraScanning() {
            try {
                log('🔍 Starting camera scanning...', 'info');
                
                if (html5QrCode) {
                    await stopScanning();
                }

                html5QrCode = new Html5Qrcode("reader");
                
                const config = {
                    fps: 10,
                    qrbox: { width: 250, height: 250 },
                    aspectRatio: 1.0,
                    disableFlip: false,
                    verbose: true
                };

                await html5QrCode.start(
                    { facingMode: "environment" },
                    config,
                    (decodedText, decodedResult) => {
                        log(`✅ QR Code detected: ${decodedText}`, 'success');
                        log(`📊 Scan result details: ${JSON.stringify(decodedResult.result)}`, 'info');
                        
                        // Validate if it's a Verifai QR code
                        if (decodedText.includes('0x') && decodedText.includes(',')) {
                            log('✅ Valid Verifai QR code format detected!', 'success');
                        } else {
                            log('⚠️ QR code detected but not in Verifai format', 'warning');
                        }
                    },
                    (errorMessage) => {
                        // Only log significant errors, not the constant "no QR found" messages
                        if (!errorMessage.includes('NotFoundException') && 
                            !errorMessage.includes('No MultiFormat Readers')) {
                            log(`⚠️ Scan error: ${errorMessage}`, 'warning');
                        }
                    }
                );

                isScanning = true;
                updateButtons();
                log('✅ Camera scanning started successfully', 'success');

            } catch (error) {
                log(`❌ Failed to start camera: ${error.message}`, 'error');
                
                if (error.message.includes('Permission')) {
                    log('💡 Camera permission denied. Please allow camera access and try again.', 'warning');
                } else if (error.message.includes('NotFoundError')) {
                    log('💡 No camera found. Please check if your device has a camera.', 'warning');
                } else if (error.message.includes('NotAllowedError')) {
                    log('💡 Camera access blocked. Check browser settings and permissions.', 'warning');
                }
            }
        }

        async function stopScanning() {
            if (html5QrCode && isScanning) {
                try {
                    await html5QrCode.stop();
                    log('⏹️ Scanning stopped', 'info');
                } catch (error) {
                    log(`⚠️ Error stopping scanner: ${error.message}`, 'warning');
                }
            }
            isScanning = false;
            updateButtons();
        }

        function testFileUpload() {
            document.getElementById('fileInput').click();
        }

        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            log(`📁 Testing file upload: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`, 'info');

            try {
                if (!html5QrCode) {
                    html5QrCode = new Html5Qrcode("reader");
                }

                const result = await html5QrCode.scanFile(file, true);
                log(`✅ File scan successful: ${result}`, 'success');
                
                if (result.includes('0x') && result.includes(',')) {
                    log('✅ Valid Verifai QR code format in file!', 'success');
                } else {
                    log('⚠️ QR code found in file but not in Verifai format', 'warning');
                }

            } catch (error) {
                log(`❌ File scan failed: ${error.message}`, 'error');
                
                if (error.message.includes('NotFoundException')) {
                    log('💡 No QR code found in the image. Try a clearer image with a visible QR code.', 'warning');
                } else if (error.message.includes('No MultiFormat Readers')) {
                    log('💡 QR code format not recognized. Ensure the image contains a standard QR code.', 'warning');
                }
            }

            // Reset file input
            event.target.value = '';
        }

        // Generate test QR code
        function generateTestQR() {
            const testData = "0x2fcc261bB32262a150E4905F6d550D4FF05bC582,TEST123";
            const qrDiv = document.getElementById('test-qr-code');
            
            // Simple QR code generation using a service
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(testData)}`;
            qrDiv.innerHTML = `<img src="${qrUrl}" alt="Test QR Code" style="max-width: 200px;">`;
        }

        // Initialize
        window.addEventListener('load', () => {
            log('🚀 QR Scanner Test Tool loaded', 'info');
            generateTestQR();
            updateButtons();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (html5QrCode && isScanning) {
                html5QrCode.stop().catch(() => {});
            }
        });
    </script>
</body>
</html>
