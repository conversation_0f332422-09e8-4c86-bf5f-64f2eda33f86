import { useEffect, useRef, useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  Stack,
  alpha,
  useTheme,
  Fade,
  CircularProgress
} from '@mui/material';
import {
  CameraAlt,
  Upload,
  Refresh,
  CheckCircle
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';

// Scanning animation
const scanLine = keyframes`
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(300px);
    opacity: 0;
  }
`;

const ScannerContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  maxWidth: '400px',
  margin: '0 auto',
  borderRadius: '20px',
  overflow: 'hidden',
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
  border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
}));

const VideoContainer = styled(Box)({
  position: 'relative',
  width: '100%',
  height: '300px',
  overflow: 'hidden',
  borderRadius: '16px',
  background: '#000',
});

const ScanOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '200px',
    height: '200px',
    border: `3px solid ${theme.palette.primary.main}`,
    borderRadius: '16px',
    boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.5)}`,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '180px',
    height: '3px',
    background: `linear-gradient(90deg, transparent, ${theme.palette.primary.main}, transparent)`,
    animation: `${scanLine} 2s ease-in-out infinite`,
  },
}));

const AlternativeQrScanner = ({ passData }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [stream, setStream] = useState(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const intervalRef = useRef(null);
  const theme = useTheme();

  // Simple QR detection using a more stable approach
  const detectQR = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    // Ensure video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) return;

    try {
      // Set canvas size to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw current video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Get image data for QR detection
      const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
      
      // Use a simpler QR detection method
      // This is a placeholder - in a real implementation, you'd use a QR library here
      // For now, we'll simulate detection after a few seconds
      
    } catch (err) {
      // Silently handle canvas errors
      console.debug('Canvas operation failed:', err.message);
    }
  };

  const startCamera = async () => {
    try {
      setError(null);
      setIsScanning(true);

      // Request camera access
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.play();

        // Start QR detection after video loads
        videoRef.current.onloadedmetadata = () => {
          intervalRef.current = setInterval(detectQR, 500); // Check every 500ms
        };
      }

    } catch (err) {
      console.error('Camera error:', err);
      setError(err.message);
      setIsScanning(false);
    }
  };

  const stopCamera = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setIsScanning(false);
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Simple file validation
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // For demo purposes, simulate successful QR detection
    setTimeout(() => {
      const mockQRData = "0x2fcc261bB32262a150E4905F6d550D4FF05bC582,TEST123";
      setSuccess(true);
      setTimeout(() => {
        passData(mockQRData);
      }, 1000);
    }, 1000);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <ScannerContainer>
      {error ? (
        <Fade in={true}>
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Alert severity="error" sx={{ mb: 3, borderRadius: '12px' }}>
              {error}
            </Alert>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={() => {
                setError(null);
                startCamera();
              }}
              sx={{
                borderRadius: '12px',
                px: 4,
                py: 1.5,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              }}
            >
              Try Again
            </Button>
          </Box>
        </Fade>
      ) : success ? (
        <Fade in={true}>
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <CheckCircle sx={{ fontSize: 64, color: theme.palette.success.main, mb: 2 }} />
            <Typography variant="h6" sx={{ color: theme.palette.success.main, fontWeight: 600 }}>
              QR Code Detected!
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, opacity: 0.8 }}>
              Redirecting...
            </Typography>
          </Box>
        </Fade>
      ) : isScanning ? (
        <Box>
          <VideoContainer>
            <video
              ref={videoRef}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
              playsInline
              muted
            />
            <ScanOverlay />
          </VideoContainer>
          
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
              Position QR code within the scanning area
            </Typography>
            <Button
              variant="outlined"
              onClick={stopCamera}
              sx={{ borderRadius: '12px' }}
            >
              Stop Scanning
            </Button>
          </Box>
        </Box>
      ) : (
        <Box sx={{ p: 4, textAlign: 'center' }}>
          <Stack spacing={3}>
            <Button
              variant="contained"
              size="large"
              startIcon={<CameraAlt />}
              onClick={startCamera}
              sx={{
                borderRadius: '16px',
                py: 2,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              }}
            >
              Start Camera Scanning
            </Button>
            
            <Typography variant="body2" sx={{ opacity: 0.6 }}>
              or
            </Typography>
            
            <input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
              id="file-upload"
            />
            <label htmlFor="file-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<Upload />}
                sx={{
                  borderRadius: '16px',
                  py: 2,
                  width: '100%',
                }}
              >
                Upload QR Code Image
              </Button>
            </label>
          </Stack>
        </Box>
      )}
      
      {/* Hidden canvas for image processing */}
      <canvas
        ref={canvasRef}
        style={{ display: 'none' }}
      />
    </ScannerContainer>
  );
};

export default AlternativeQrScanner;
