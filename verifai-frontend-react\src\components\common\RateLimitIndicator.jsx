import React, { useState, useEffect } from 'react';
import { Alert, Snackbar, Box, LinearProgress, Typography } from '@mui/material';
import { Warning, CheckCircle } from '@mui/icons-material';

const RateLimitIndicator = () => {
    const [isRateLimited, setIsRateLimited] = useState(false);
    const [retryCount, setRetryCount] = useState(0);
    const [showSuccess, setShowSuccess] = useState(false);

    useEffect(() => {
        // Listen for rate limit events
        const handleRateLimit = (event) => {
            setIsRateLimited(true);
            setRetryCount(event.detail?.attempt || 1);
        };

        const handleRateLimitResolved = () => {
            setIsRateLimited(false);
            setRetryCount(0);
            setShowSuccess(true);
            setTimeout(() => setShowSuccess(false), 3000);
        };

        // Custom events for rate limiting
        window.addEventListener('rateLimitExceeded', handleRateLimit);
        window.addEventListener('rateLimitResolved', handleRateLimitResolved);

        return () => {
            window.removeEventListener('rateLimitExceeded', handleRateLimit);
            window.removeEventListener('rateLimitResolved', handleRateLimitResolved);
        };
    }, []);

    if (!isRateLimited && !showSuccess) {
        return null;
    }

    return (
        <>
            {/* Rate Limit Warning */}
            <Snackbar
                open={isRateLimited}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                sx={{ mt: 8 }}
            >
                <Alert 
                    severity="warning" 
                    icon={<Warning />}
                    sx={{ 
                        minWidth: 300,
                        '& .MuiAlert-message': {
                            width: '100%'
                        }
                    }}
                >
                    <Box>
                        <Typography variant="body2" fontWeight="bold">
                            Rate Limit Exceeded
                        </Typography>
                        <Typography variant="caption" display="block">
                            Too many requests. Retrying automatically... (Attempt {retryCount})
                        </Typography>
                        <LinearProgress 
                            sx={{ 
                                mt: 1, 
                                borderRadius: 1,
                                height: 4,
                                backgroundColor: 'rgba(255, 152, 0, 0.2)',
                                '& .MuiLinearProgress-bar': {
                                    backgroundColor: '#ff9800'
                                }
                            }} 
                        />
                    </Box>
                </Alert>
            </Snackbar>

            {/* Success Message */}
            <Snackbar
                open={showSuccess}
                autoHideDuration={3000}
                onClose={() => setShowSuccess(false)}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                sx={{ mt: 8 }}
            >
                <Alert 
                    severity="success" 
                    icon={<CheckCircle />}
                    onClose={() => setShowSuccess(false)}
                >
                    <Typography variant="body2">
                        Connection restored successfully!
                    </Typography>
                </Alert>
            </Snackbar>
        </>
    );
};

export default RateLimitIndicator;
