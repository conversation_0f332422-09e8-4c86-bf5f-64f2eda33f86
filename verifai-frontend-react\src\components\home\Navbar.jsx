import * as React from "react";
import { useState } from "react";
import {
  AppBar,
  Box,
  Container,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  styled,
} from "@mui/material";

import MenuIcon from "@mui/icons-material/Menu";
import HomeIcon from "@mui/icons-material/Home";
import FeaturedPlayListIcon from "@mui/icons-material/FeaturedPlayList";
import MiscellaneousServicesIcon from "@mui/icons-material/MiscellaneousServices";
import ListAltIcon from "@mui/icons-material/ListAlt";
import ContactsIcon from "@mui/icons-material/Contacts";
import logoImg from "../../img/logo.png";
import { Link } from "react-router-dom";
import CustomButton from "./CustomButton";

// Styled Components
const NavbarLogo = styled("img")(({ theme }) => ({
  height: "40px",
  cursor: "pointer",
  [theme.breakpoints.down("sm")]: {
    height: "30px",
  },
}));

const NavLink = styled(Link)(({ theme }) => ({
  fontSize: "14px",
  color: "#4F5361",
  fontWeight: "bold",
  textDecoration: "none",
  "&:hover": {
    color: "#0F1B4C",
  },
}));

const NavbarLinksBox = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(3),
  [theme.breakpoints.down("md")]: {
    display: "none",
  },
}));

const CustomMenuIcon = styled(IconButton)(({ theme }) => ({
  color: "#0F1B4C",
  [theme.breakpoints.up("md")]: {
    display: "none",
  },
}));

export const Navbar = () => {
  const [mobileMenu, setMobileMenu] = useState(false);

  const toggleDrawer = (open) => (event) => {
    if (
      event.type === "keydown" &&
      (event.key === "Tab" || event.key === "Shift")
    ) {
      return;
    }
    setMobileMenu(open);
  };

  const navItems = [
    { text: "Home", icon: <HomeIcon />, link: "/" },
    { text: "Features", icon: <FeaturedPlayListIcon />, link: "/features" },
    { text: "Services", icon: <MiscellaneousServicesIcon />, link: "/services" },
    { text: "Listed", icon: <ListAltIcon />, link: "/listed" },
    { text: "Contact", icon: <ContactsIcon />, link: "/contact" },
  ];

  const drawerList = (
    <Box
      sx={{ width: 250 }}
      role="presentation"
      onClick={toggleDrawer(false)}
      onKeyDown={toggleDrawer(false)}
    >
      <List>
        {navItems.map(({ text, icon, link }) => (
          <ListItem key={text} disablePadding>
            <ListItemButton component={Link} to={link}>
              <ListItemIcon>{icon}</ListItemIcon>
              <ListItemText primary={text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <AppBar position="static" color="transparent" elevation={0}>
      <Container maxWidth="lg">
        <Toolbar disableGutters sx={{ justifyContent: "space-between" }}>
          {/* Left: Logo & Menu */}
          <Box display="flex" alignItems="center" gap={2}>
            <CustomMenuIcon onClick={toggleDrawer(true)}>
              <MenuIcon />
            </CustomMenuIcon>
            <Drawer anchor="left" open={mobileMenu} onClose={toggleDrawer(false)}>
              {drawerList}
            </Drawer>
            <Link to="/">
              <NavbarLogo src={logoImg} alt="Identeefi Logo" />
            </Link>
          </Box>

          {/* Center: Nav links */}
          <NavbarLinksBox>
            {navItems.map(({ text, link }) => (
              <NavLink key={text} to={link}>
                {text}
              </NavLink>
            ))}
          </NavbarLinksBox>

          {/* Right: Login */}
          <Box>
            <Link to="/login" style={{ textDecoration: "none" }}>
              <CustomButton
                backgroundColor="#0F1B4C"
                color="#fff"
                buttonText="Login"
              />
            </Link>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Navbar;
