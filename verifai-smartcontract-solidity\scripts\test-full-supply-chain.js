const hre = require("hardhat");

async function main() {
    console.log("🔍 Testing full supply chain workflow...\n");

    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully\n");
        
        // Test product data
        const productData = {
            name: "dffg",
            brand: "dffg",
            serialNumber: "dffg",
            description: "Test product for supply chain tracking",
            image: "test-image.jpg",
            actor: "manufacturer",
            location: "1.8565, 103.0869",
            timestamp: Math.floor(Date.now() / 1000).toString()
        };
        
        console.log("📦 Step 1: Registering product...");
        console.log("Product data:", productData);
        
        try {
            // Check if product already exists
            await contract.getProduct(productData.serialNumber);
            console.log("✅ Product already exists, skipping registration");
        } catch (error) {
            // Product doesn't exist, register it
            console.log("⏳ Registering new product...");
            const registerTx = await contract.registerProduct(
                productData.name,
                productData.brand,
                productData.serialNumber,
                productData.description,
                productData.image,
                productData.actor,
                productData.location,
                productData.timestamp
            );
            
            console.log("⏳ Transaction submitted:", registerTx.hash);
            await registerTx.wait();
            console.log("✅ Product registered successfully!");
        }
        
        // Step 2: Supplier receives product
        console.log("\n📦 Step 2: Supplier receives product...");
        const supplierTx = await contract.addProductHistory(
            productData.serialNumber,
            "Test Supplier Company",
            "Supplier Warehouse, Singapore",
            (parseInt(productData.timestamp) + 3600).toString(), // 1 hour later
            false // not sold
        );
        
        console.log("⏳ Supplier transaction submitted:", supplierTx.hash);
        await supplierTx.wait();
        console.log("✅ Supplier update successful!");
        
        // Step 3: Retailer receives product
        console.log("\n📦 Step 3: Retailer receives product...");
        const retailerReceiveTx = await contract.addProductHistory(
            productData.serialNumber,
            "Test Retail Store",
            "Retail Shop, Orchard Road",
            (parseInt(productData.timestamp) + 7200).toString(), // 2 hours later
            false // not sold yet
        );
        
        console.log("⏳ Retailer receive transaction submitted:", retailerReceiveTx.hash);
        await retailerReceiveTx.wait();
        console.log("✅ Retailer receive update successful!");
        
        // Step 4: Product sold to customer
        console.log("\n📦 Step 4: Product sold to customer...");
        const saleTx = await contract.addProductHistory(
            productData.serialNumber,
            "Test Retail Store",
            "Retail Shop, Orchard Road",
            (parseInt(productData.timestamp) + 10800).toString(), // 3 hours later
            true // SOLD!
        );
        
        console.log("⏳ Sale transaction submitted:", saleTx.hash);
        await saleTx.wait();
        console.log("✅ Sale update successful!");
        
        // Step 5: Verify complete history
        console.log("\n🔍 Step 5: Verifying complete supply chain history...");
        const product = await contract.getProduct(productData.serialNumber);
        
        console.log("✅ Product retrieved successfully!");
        console.log("📋 Complete Product History:");
        console.log(`   Total History Entries: ${product[5].length}`);
        
        for (let i = 0; i < product[5].length; i++) {
            const historyEntry = product[5][i];
            console.log(`\n   📍 Entry ${i + 1}:`);
            console.log(`     Actor: ${historyEntry.actor}`);
            console.log(`     Location: ${historyEntry.location}`);
            console.log(`     Timestamp: ${historyEntry.timestamp}`);
            console.log(`     Date: ${new Date(historyEntry.timestamp * 1000).toLocaleString()}`);
            console.log(`     Is Sold: ${historyEntry.isSold}`);
        }
        
        console.log("\n🎉 Supply Chain Test Complete!");
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        console.log(`📍 Contract Address: ${contractAddress}`);
        console.log(`🔗 QR Code Data: ${contractAddress},${productData.serialNumber}`);
        console.log(`📱 Test URL: http://localhost:5173/product?qr=${contractAddress},${productData.serialNumber}`);
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        console.log("\n✅ Expected Results:");
        console.log("1. Product page should show 4 history entries");
        console.log("2. History should show: Manufacturer → Supplier → Retailer (receive) → Retailer (sold)");
        console.log("3. Sale status should show 'Sold'");
        console.log("4. Timeline should display complete supply chain journey");
        
        return productData.serialNumber;
        
    } catch (error) {
        console.log("❌ Error:", error.message);
        console.log("Full error:", error);
    }
}

// Execute the test
if (require.main === module) {
    main()
        .then((serialNumber) => {
            console.log(`\n🎯 Test completed for product: ${serialNumber}`);
            console.log("Now scan the QR code or visit the product page to see the complete history!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Test failed:", error);
            process.exit(1);
        });
}

module.exports = main;
