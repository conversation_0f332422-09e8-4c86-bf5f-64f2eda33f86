import axios from 'axios';

// Rate limiting and retry utility
class ApiRateLimiter {
    constructor() {
        this.requestQueue = [];
        this.isProcessing = false;
        this.lastRequestTime = 0;
        this.minInterval = 100; // Minimum 100ms between requests
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second initial retry delay
    }

    // Add delay between requests
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Process request queue with rate limiting
    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        while (this.requestQueue.length > 0) {
            const { request, resolve, reject } = this.requestQueue.shift();
            
            try {
                // Ensure minimum interval between requests
                const now = Date.now();
                const timeSinceLastRequest = now - this.lastRequestTime;
                if (timeSinceLastRequest < this.minInterval) {
                    await this.delay(this.minInterval - timeSinceLastRequest);
                }

                const response = await this.executeWithRetry(request);
                this.lastRequestTime = Date.now();
                resolve(response);
            } catch (error) {
                reject(error);
            }

            // Small delay between queue items
            await this.delay(50);
        }

        this.isProcessing = false;
    }

    // Execute request with retry logic
    async executeWithRetry(request, attempt = 1) {
        try {
            const response = await request();
            // Emit success event if this was a retry
            if (attempt > 1) {
                window.dispatchEvent(new CustomEvent('rateLimitResolved'));
            }
            return response;
        } catch (error) {
            // Handle rate limiting (429) and server errors (5xx)
            if ((error.response?.status === 429 || error.response?.status >= 500) && attempt < this.retryAttempts) {
                // Emit rate limit event
                window.dispatchEvent(new CustomEvent('rateLimitExceeded', {
                    detail: { attempt, maxAttempts: this.retryAttempts, status: error.response?.status }
                }));

                const delay = this.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
                console.warn(`Request failed (attempt ${attempt}/${this.retryAttempts}), retrying in ${delay}ms...`);
                await this.delay(delay);
                return this.executeWithRetry(request, attempt + 1);
            }
            throw error;
        }
    }

    // Add request to queue
    async queueRequest(request) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ request, resolve, reject });
            this.processQueue();
        });
    }
}

// Global rate limiter instance
const rateLimiter = new ApiRateLimiter();

// Enhanced axios instance with rate limiting and auth
const apiClient = axios.create({
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000',
    timeout: 10000, // 10 second timeout
    withCredentials: true, // Include cookies for refresh tokens
});

// Request interceptor to add auth headers and rate limiting
apiClient.interceptors.request.use(
    (config) => {
        // Add timestamp to prevent caching issues
        config.params = {
            ...config.params,
            _t: Date.now()
        };

        // Add auth header if token exists
        const token = localStorage.getItem('verifai_access_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor to handle errors and token refresh
apiClient.interceptors.response.use(
    (response) => {
        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // Handle token expiration
        if (error.response?.status === 401 &&
            error.response?.data?.code === 'TOKEN_EXPIRED' &&
            !originalRequest._retry) {

            originalRequest._retry = true;

            try {
                // Try to refresh token
                const refreshResponse = await axios.post(
                    `${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/auth/refresh`,
                    {},
                    { withCredentials: true }
                );

                if (refreshResponse.data.accessToken) {
                    // Update stored token
                    localStorage.setItem('verifai_access_token', refreshResponse.data.accessToken);

                    // Update the original request with new token
                    originalRequest.headers.Authorization = `Bearer ${refreshResponse.data.accessToken}`;

                    // Retry the original request
                    return apiClient(originalRequest);
                }
            } catch (refreshError) {
                console.error('Token refresh failed:', refreshError);

                // Clear auth data and redirect to login
                localStorage.removeItem('verifai_access_token');
                localStorage.removeItem('verifai_user');

                // Dispatch auth state change event
                window.dispatchEvent(new CustomEvent('authStateChanged', {
                    detail: { authenticated: false, reason: 'TOKEN_REFRESH_FAILED' }
                }));

                return Promise.reject(refreshError);
            }
        }

        // Handle other errors
        if (error.response?.status === 429) {
            console.warn('Rate limit exceeded. Please slow down your requests.');
        } else if (error.response?.status >= 500) {
            console.error('Server error occurred:', error.response.status);
        } else if (error.code === 'ECONNABORTED') {
            console.error('Request timeout');
        }

        return Promise.reject(error);
    }
);

// Wrapper function for API calls with rate limiting
export const makeApiRequest = async (requestFunction) => {
    return rateLimiter.queueRequest(requestFunction);
};

// Specific API functions with rate limiting
export const apiGet = async (url, config = {}) => {
    return makeApiRequest(() => apiClient.get(url, config));
};

export const apiPost = async (url, data, config = {}) => {
    return makeApiRequest(() => apiClient.post(url, data, config));
};

export const apiPut = async (url, data, config = {}) => {
    return makeApiRequest(() => apiClient.put(url, data, config));
};

export const apiDelete = async (url, config = {}) => {
    return makeApiRequest(() => apiClient.delete(url, config));
};

// Debounced function utility
export const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(null, args), delay);
    };
};

// Throttled function utility
export const throttle = (func, limit) => {
    let inThrottle;
    return (...args) => {
        if (!inThrottle) {
            func.apply(null, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

export default apiClient;
