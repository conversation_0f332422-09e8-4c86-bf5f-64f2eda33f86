import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>ton,
  Alert,
  Stack,
  Chip,
  Divider,
  Link
} from '@mui/material';
import {
  AccountBalanceWallet,
  Download,
  CheckCircle,
  Warning,
  Refresh
} from '@mui/icons-material';

const MetaMaskChecker = ({ onMetaMaskReady }) => {
  const [metaMaskStatus, setMetaMaskStatus] = useState({
    installed: false,
    connected: false,
    account: null,
    chainId: null,
    checking: true
  });

  const checkMetaMaskStatus = async () => {
    setMetaMaskStatus(prev => ({ ...prev, checking: true }));

    try {
      // Check if MetaMask is installed
      if (typeof window.ethereum === 'undefined') {
        setMetaMaskStatus({
          installed: false,
          connected: false,
          account: null,
          chainId: null,
          checking: false
        });
        return;
      }

      // Check if it's MetaMask
      const isMetaMask = window.ethereum.isMetaMask;
      
      if (!isMetaMask) {
        setMetaMaskStatus({
          installed: false,
          connected: false,
          account: null,
          chainId: null,
          checking: false
        });
        return;
      }

      // Get accounts
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      const chainId = await window.ethereum.request({ method: 'eth_chainId' });

      const status = {
        installed: true,
        connected: accounts.length > 0,
        account: accounts.length > 0 ? accounts[0] : null,
        chainId: chainId,
        checking: false
      };

      setMetaMaskStatus(status);

      // Notify parent component if MetaMask is ready
      if (status.installed && status.connected && onMetaMaskReady) {
        onMetaMaskReady(status);
      }

    } catch (error) {
      console.error('Error checking MetaMask status:', error);
      setMetaMaskStatus({
        installed: false,
        connected: false,
        account: null,
        chainId: null,
        checking: false
      });
    }
  };

  const connectMetaMask = async () => {
    try {
      if (typeof window.ethereum === 'undefined') {
        alert('MetaMask is not installed. Please install it first.');
        return;
      }

      const accounts = await window.ethereum.request({ 
        method: 'eth_requestAccounts' 
      });

      if (accounts.length > 0) {
        await checkMetaMaskStatus();
      }
    } catch (error) {
      console.error('Error connecting to MetaMask:', error);
      if (error.code === 4001) {
        alert('Please approve the connection request in MetaMask.');
      } else {
        alert('Error connecting to MetaMask. Please try again.');
      }
    }
  };

  useEffect(() => {
    checkMetaMaskStatus();

    // Listen for account changes
    if (window.ethereum) {
      const handleAccountsChanged = (accounts) => {
        checkMetaMaskStatus();
      };

      const handleChainChanged = (chainId) => {
        checkMetaMaskStatus();
      };

      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);

      return () => {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
      };
    }
  }, []);

  if (metaMaskStatus.checking) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Checking MetaMask Status...
        </Typography>
      </Paper>
    );
  }

  if (!metaMaskStatus.installed) {
    return (
      <Paper sx={{ p: 3, border: '2px solid', borderColor: 'warning.main' }}>
        <Stack spacing={3} alignItems="center">
          <Warning color="warning" sx={{ fontSize: 48 }} />
          
          <Typography variant="h5" fontWeight={600} color="warning.main">
            MetaMask Not Found
          </Typography>
          
          <Typography variant="body1" textAlign="center" color="text.secondary">
            MetaMask extension is required to interact with the blockchain. 
            Please install MetaMask to continue with product registration.
          </Typography>

          <Alert severity="info" sx={{ width: '100%' }}>
            <Typography variant="body2">
              MetaMask is a secure cryptocurrency wallet that allows you to interact with blockchain applications.
            </Typography>
          </Alert>

          <Stack direction="row" spacing={2}>
            <Button
              variant="contained"
              startIcon={<Download />}
              href="https://metamask.io/download/"
              target="_blank"
              rel="noopener noreferrer"
              sx={{ minWidth: 200 }}
            >
              Install MetaMask
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={checkMetaMaskStatus}
            >
              Check Again
            </Button>
          </Stack>

          <Typography variant="caption" color="text.secondary">
            After installing MetaMask, refresh this page or click "Check Again"
          </Typography>
        </Stack>
      </Paper>
    );
  }

  if (!metaMaskStatus.connected) {
    return (
      <Paper sx={{ p: 3, border: '2px solid', borderColor: 'info.main' }}>
        <Stack spacing={3} alignItems="center">
          <AccountBalanceWallet color="info" sx={{ fontSize: 48 }} />
          
          <Typography variant="h5" fontWeight={600} color="info.main">
            Connect MetaMask Wallet
          </Typography>
          
          <Typography variant="body1" textAlign="center" color="text.secondary">
            MetaMask is installed but not connected. Please connect your wallet to continue.
          </Typography>

          <Button
            variant="contained"
            size="large"
            startIcon={<AccountBalanceWallet />}
            onClick={connectMetaMask}
            sx={{ minWidth: 200 }}
          >
            Connect Wallet
          </Button>

          <Typography variant="caption" color="text.secondary">
            Click "Connect Wallet" and approve the connection in MetaMask
          </Typography>
        </Stack>
      </Paper>
    );
  }

  // MetaMask is connected - show status
  return (
    <Paper sx={{ p: 2, border: '2px solid', borderColor: 'success.main', mb: 2 }}>
      <Stack direction="row" alignItems="center" spacing={2}>
        <CheckCircle color="success" />
        
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="subtitle1" fontWeight={600} color="success.main">
            MetaMask Connected
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Account: {metaMaskStatus.account?.slice(0, 6)}...{metaMaskStatus.account?.slice(-4)}
          </Typography>
        </Box>

        <Chip 
          label="Ready" 
          color="success" 
          variant="outlined" 
          size="small" 
        />
      </Stack>
    </Paper>
  );
};

export default MetaMaskChecker;
