import { useLocation, Navigate, Outlet } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import useAuth from '../hooks/useAuth';

const RequireAuth = ({ allowedRoles }) => {
    const { isAuthenticated, isLoading, user } = useAuth();
    const location = useLocation();

    // Show loading spinner while checking authentication
    if (isLoading) {
        return (
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '50vh',
                    gap: 2
                }}
            >
                <CircularProgress size={40} />
                <Typography variant="body2" color="text.secondary">
                    Verifying authentication...
                </Typography>
            </Box>
        );
    }

    // Check if user is authenticated
    if (!isAuthenticated) {
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    // Check role-based access if roles are specified
    if (allowedRoles && allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {
        return (
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '50vh',
                    gap: 2,
                    textAlign: 'center'
                }}
            >
                <Typography variant="h5" color="error">
                    Access Denied
                </Typography>
                <Typography variant="body1" color="text.secondary">
                    You don't have permission to access this page.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    Required role(s): {allowedRoles.join(', ')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    Your role: {user?.role || 'Unknown'}
                </Typography>
            </Box>
        );
    }

    return <Outlet />;
};

export default RequireAuth;