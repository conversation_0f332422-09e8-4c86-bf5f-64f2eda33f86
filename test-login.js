const { Client } = require('pg');
const bcrypt = require('bcrypt');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

async function testLogin() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        // Check existing accounts
        const accounts = await client.query('SELECT username, role, first_login FROM auth ORDER BY id');
        
        console.log('\n📋 Existing accounts:');
        if (accounts.rows.length === 0) {
            console.log('  No accounts found');
        } else {
            accounts.rows.forEach(account => {
                console.log(`  ${account.username} (${account.role}) - first_login: ${account.first_login}`);
            });
        }

        // Test password verification for admin account
        if (accounts.rows.length > 0) {
            const adminAccount = accounts.rows.find(acc => acc.role === 'admin');
            if (adminAccount) {
                console.log(`\n🔍 Testing password verification for admin: ${adminAccount.username}`);
                
                // Get the stored password hash
                const passwordQuery = await client.query('SELECT password FROM auth WHERE username = $1', [adminAccount.username]);
                if (passwordQuery.rows.length > 0) {
                    const storedHash = passwordQuery.rows[0].password;
                    console.log(`Stored password hash: ${storedHash.substring(0, 20)}...`);
                    
                    // Test common passwords
                    const testPasswords = ['admin', 'password', 'admin123', 'Admin123!', '123456'];
                    
                    for (const testPwd of testPasswords) {
                        try {
                            const isMatch = await bcrypt.compare(testPwd, storedHash);
                            if (isMatch) {
                                console.log(`✅ Password '${testPwd}' matches!`);
                                break;
                            } else {
                                console.log(`❌ Password '${testPwd}' does not match`);
                            }
                        } catch (error) {
                            console.log(`❌ Error testing password '${testPwd}': ${error.message}`);
                        }
                    }
                }
            } else {
                console.log('No admin account found');
            }
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the test
testLogin();
