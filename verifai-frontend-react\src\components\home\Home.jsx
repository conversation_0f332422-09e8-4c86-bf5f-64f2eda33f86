import { Box } from "@mui/material";
import Hero from "./Hero";
import Features from "./Features";
import Footer from "./Footer";
import ParticleField from "../common/ParticleField";

const Home = () => {
    return (
        <Box sx={{ position: 'relative', overflow: 'hidden' }}>
            {/* Beautiful Particle Animation Background */}
            <ParticleField density="medium" animated={true} />

            <Hero />
            <Features />
            <Footer />
        </Box>
    );
}

export default Home;