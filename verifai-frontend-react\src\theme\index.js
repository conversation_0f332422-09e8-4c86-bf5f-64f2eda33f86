import { createTheme, alpha } from '@mui/material/styles';

// Next-generation cyberpunk color palette with holographic effects
const colors = {
  primary: {
    50: '#f0f4ff',
    100: '#e0e7ff',
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#6366f1',
    600: '#4f46e5',
    700: '#4338ca',
    800: '#3730a3',
    900: '#312e81',
    // Advanced holographic gradients
    gradient: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 25%, #a855f7 50%, #ec4899 75%, #f59e0b 100%)',
    holographic: 'linear-gradient(45deg, #6366f1, #8b5cf6, #a855f7, #ec4899, #f59e0b, #10b981, #06b6d4)',
    neon: '#00d4ff',
    plasma: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    chrome: 'linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%)',
  },
  secondary: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
    gradient: 'linear-gradient(135deg, #10b981 0%, #059669 25%, #047857 50%, #06b6d4 75%, #0891b2 100%)',
    holographic: 'linear-gradient(45deg, #10b981, #059669, #047857, #06b6d4, #0891b2, #0284c7, #0369a1)',
    neon: '#00ff88',
    plasma: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
    chrome: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  },
  accent: {
    50: '#fef7ff',
    100: '#fce7ff',
    200: '#f8d4fe',
    300: '#f0abfc',
    400: '#e879f9',
    500: '#d946ef',
    600: '#c026d3',
    700: '#a21caf',
    800: '#86198f',
    900: '#701a75',
    gradient: 'linear-gradient(135deg, #d946ef 0%, #c026d3 25%, #a21caf 50%, #ec4899 75%, #f97316 100%)',
    holographic: 'linear-gradient(45deg, #d946ef, #c026d3, #a21caf, #ec4899, #f97316, #eab308, #22c55e)',
    neon: '#ff00ff',
    plasma: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    chrome: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  },
  cyber: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6',
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a',
    gradient: 'linear-gradient(135deg, #14b8a6 0%, #0d9488 25%, #0f766e 50%, #06b6d4 75%, #0284c7 100%)',
    holographic: 'linear-gradient(45deg, #14b8a6, #0d9488, #0f766e, #06b6d4, #0284c7, #0369a1, #1e40af)',
    neon: '#00ffff',
    plasma: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    chrome: 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)',
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    gradient: 'linear-gradient(135deg, #22c55e 0%, #16a34a 25%, #15803d 50%, #10b981 75%, #059669 100%)',
    holographic: 'linear-gradient(45deg, #22c55e, #16a34a, #15803d, #10b981, #059669, #047857, #065f46)',
    neon: '#00ff00',
    plasma: 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)',
    chrome: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    gradient: 'linear-gradient(135deg, #f59e0b 0%, #d97706 25%, #b45309 50%, #f97316 75%, #ea580c 100%)',
    holographic: 'linear-gradient(45deg, #f59e0b, #d97706, #b45309, #f97316, #ea580c, #dc2626, #b91c1c)',
    neon: '#ffff00',
    plasma: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    chrome: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    gradient: 'linear-gradient(135deg, #ef4444 0%, #dc2626 25%, #b91c1c 50%, #ec4899 75%, #be185d 100%)',
    holographic: 'linear-gradient(45deg, #ef4444, #dc2626, #b91c1c, #ec4899, #be185d, #9d174d, #831843)',
    neon: '#ff0000',
    plasma: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    chrome: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  // Advanced cyberpunk color schemes
  matrix: {
    50: '#f0fff4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    gradient: 'linear-gradient(135deg, #00ff41 0%, #00d4aa 25%, #00b4d8 50%, #0077b6 75%, #023e8a 100%)',
    holographic: 'linear-gradient(45deg, #00ff41, #00d4aa, #00b4d8, #0077b6, #023e8a, #03045e)',
    neon: '#00ff41',
    plasma: 'linear-gradient(135deg, #00ff41 0%, #00d4aa 100%)',
  },
  synthwave: {
    50: '#fdf2f8',
    100: '#fce7f3',
    200: '#fbcfe8',
    300: '#f9a8d4',
    400: '#f472b6',
    500: '#ec4899',
    600: '#db2777',
    700: '#be185d',
    800: '#9d174d',
    900: '#831843',
    gradient: 'linear-gradient(135deg, #ff006e 0%, #8338ec 25%, #3a86ff 50%, #06ffa5 75%, #ffbe0b 100%)',
    holographic: 'linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5, #ffbe0b, #fb5607)',
    neon: '#ff006e',
    plasma: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
  },
  quantum: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
    holographic: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe)',
    neon: '#667eea',
    plasma: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  },
};

// Typography configuration
const typography = {
  fontFamily: [
    '"Inter"',
    '"Roboto"',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif',
  ].join(','),
  h1: {
    fontSize: '2.5rem',
    fontWeight: 700,
    lineHeight: 1.2,
    letterSpacing: '-0.025em',
  },
  h2: {
    fontSize: '2rem',
    fontWeight: 600,
    lineHeight: 1.3,
    letterSpacing: '-0.025em',
  },
  h3: {
    fontSize: '1.75rem',
    fontWeight: 600,
    lineHeight: 1.3,
    letterSpacing: '-0.02em',
  },
  h4: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.4,
    letterSpacing: '-0.02em',
  },
  h5: {
    fontSize: '1.25rem',
    fontWeight: 600,
    lineHeight: 1.4,
    letterSpacing: '-0.01em',
  },
  h6: {
    fontSize: '1.125rem',
    fontWeight: 600,
    lineHeight: 1.4,
    letterSpacing: '-0.01em',
  },
  body1: {
    fontSize: '1rem',
    lineHeight: 1.6,
    letterSpacing: '0.00938em',
  },
  body2: {
    fontSize: '0.875rem',
    lineHeight: 1.5,
    letterSpacing: '0.01071em',
  },
  caption: {
    fontSize: '0.75rem',
    lineHeight: 1.4,
    letterSpacing: '0.03333em',
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 600,
    lineHeight: 1.5,
    letterSpacing: '0.02857em',
    textTransform: 'none',
  },
};

// Global Icon Enhancement System for Maximum Visibility and Contrast
const getIconContrastColor = (theme, variant = 'default', context = 'general') => {
  const isDark = theme.palette.mode === 'dark';

  // Context-specific color calculations for different backgrounds
  const contextColors = {
    general: {
      default: isDark ? alpha('#ffffff', 0.87) : alpha('#000000', 0.87),
      primary: isDark ? theme.palette.primary.light : theme.palette.primary.main,
      secondary: isDark ? theme.palette.secondary.light : theme.palette.secondary.main,
      success: isDark ? theme.palette.success.light : theme.palette.success.main,
      warning: isDark ? theme.palette.warning.light : theme.palette.warning.main,
      error: isDark ? theme.palette.error.light : theme.palette.error.main,
      muted: isDark ? alpha('#ffffff', 0.6) : alpha('#000000', 0.6),
    },
    glassmorphism: {
      default: isDark ? alpha('#ffffff', 0.9) : alpha('#000000', 0.9),
      primary: isDark ? theme.palette.primary.light : theme.palette.primary.dark,
      secondary: isDark ? theme.palette.secondary.light : theme.palette.secondary.dark,
      success: isDark ? theme.palette.success.light : theme.palette.success.dark,
      warning: isDark ? theme.palette.warning.light : theme.palette.warning.dark,
      error: isDark ? theme.palette.error.light : theme.palette.error.dark,
      muted: isDark ? alpha('#ffffff', 0.75) : alpha('#000000', 0.75),
    },
    navigation: {
      default: isDark ? alpha('#ffffff', 0.8) : alpha('#000000', 0.8),
      primary: theme.palette.primary.main,
      secondary: theme.palette.secondary.main,
      success: theme.palette.success.main,
      warning: theme.palette.warning.main,
      error: theme.palette.error.main,
      muted: isDark ? alpha('#ffffff', 0.5) : alpha('#000000', 0.5),
    }
  };

  return contextColors[context]?.[variant] || contextColors.general[variant] || contextColors.general.default;
};

const getIconShadow = (theme, intensity = 'medium', context = 'general') => {
  const isDark = theme.palette.mode === 'dark';

  const shadows = {
    light: isDark
      ? `0 1px 2px ${alpha('#000000', 0.4)}, 0 0 4px ${alpha('#ffffff', 0.1)}`
      : `0 1px 1px ${alpha('#ffffff', 0.9)}, 0 0 2px ${alpha('#000000', 0.1)}`,
    medium: isDark
      ? `0 1px 3px ${alpha('#000000', 0.6)}, 0 0 6px ${alpha('#ffffff', 0.15)}`
      : `0 1px 2px ${alpha('#ffffff', 1)}, 0 0 4px ${alpha('#000000', 0.15)}`,
    strong: isDark
      ? `0 2px 4px ${alpha('#000000', 0.8)}, 0 0 8px ${alpha('#ffffff', 0.2)}`
      : `0 2px 3px ${alpha('#ffffff', 1)}, 0 0 6px ${alpha('#000000', 0.2)}`,
  };

  return shadows[intensity] || shadows.medium;
};

// Advanced component customizations with futuristic styling and enhanced icon visibility
const components = {
  MuiButton: {
    styleOverrides: {
      root: ({ theme }) => ({
        borderRadius: '16px',
        textTransform: 'none',
        fontWeight: 600,
        padding: '14px 32px',
        fontSize: '0.95rem',
        boxShadow: 'none',
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: '-100%',
          width: '100%',
          height: '100%',
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
          transition: 'left 0.5s',
        },
        '&:hover': {
          transform: 'translateY(-2px)',
          '&::before': {
            left: '100%',
          },
          // Enhanced icon visibility in buttons on hover
          '& .MuiButton-startIcon, & .MuiButton-endIcon': {
            '& .MuiSvgIcon-root': {
              filter: `brightness(1.2) contrast(1.2) drop-shadow(${getIconShadow(theme, 'strong')})`,
              transform: 'scale(1.05)',
            },
          },
        },
        '&:active': {
          transform: 'translateY(0)',
        },

        // Enhanced icon visibility in buttons
        '& .MuiButton-startIcon, & .MuiButton-endIcon': {
          '& .MuiSvgIcon-root': {
            filter: `brightness(1.1) contrast(1.1) drop-shadow(${getIconShadow(theme, 'medium')})`,
            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          },
        },
      }),
      contained: {
        background: colors.primary.gradient,
        boxShadow: `0 8px 25px ${colors.primary[500]}40`,
        '&:hover': {
          boxShadow: `0 12px 35px ${colors.primary[500]}60`,
        },
      },
      outlined: {
        borderWidth: '2px',
        backdropFilter: 'blur(10px)',
        background: 'rgba(255, 255, 255, 0.05)',
        '&:hover': {
          borderWidth: '2px',
          background: 'rgba(255, 255, 255, 0.1)',
          boxShadow: `0 8px 25px ${colors.primary[500]}30`,
        },
      },
    },
  },
  MuiCard: {
    styleOverrides: {
      root: {
        borderRadius: '24px',
        background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(255,255,255,0.2)',
        boxShadow: '0 8px 32px rgba(31, 38, 135, 0.15)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: colors.primary.gradient,
        },
        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: '0 20px 40px rgba(31, 38, 135, 0.25)',
          border: `1px solid ${colors.primary[300]}40`,
        },
      },
    },
  },
  MuiPaper: {
    styleOverrides: {
      root: {
        borderRadius: '20px',
        background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.8) 100%)',
        backdropFilter: 'blur(15px)',
        border: '1px solid rgba(255,255,255,0.2)',
        boxShadow: '0 8px 32px rgba(31, 38, 135, 0.1)',
      },
    },
  },
  MuiTextField: {
    styleOverrides: {
      root: {
        '& .MuiOutlinedInput-root': {
          borderRadius: '16px',
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '& fieldset': {
            border: `2px solid ${colors.primary[200]}60`,
          },
          '&:hover': {
            background: 'rgba(255, 255, 255, 0.9)',
            '& fieldset': {
              border: `2px solid ${colors.primary[400]}80`,
              boxShadow: `0 0 20px ${colors.primary[400]}30`,
            },
          },
          '&.Mui-focused': {
            background: 'rgba(255, 255, 255, 0.95)',
            '& fieldset': {
              border: `2px solid ${colors.primary[500]}`,
              boxShadow: `0 0 25px ${colors.primary[500]}40`,
            },
          },
        },
        '& .MuiInputLabel-root': {
          fontWeight: 600,
          '&.Mui-focused': {
            color: colors.primary[600],
          },
        },
      },
    },
  },
  MuiChip: {
    styleOverrides: {
      root: {
        borderRadius: '12px',
        fontWeight: 600,
        backdropFilter: 'blur(10px)',
        background: 'rgba(255, 255, 255, 0.2)',
        border: '1px solid rgba(255, 255, 255, 0.3)',
        '&.MuiChip-filled': {
          background: colors.primary.gradient,
          color: 'white',
          boxShadow: `0 4px 15px ${colors.primary[500]}40`,
        },
      },
    },
  },
  MuiAvatar: {
    styleOverrides: {
      root: {
        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
        border: '2px solid rgba(255, 255, 255, 0.3)',
        background: colors.primary.gradient,
      },
    },
  },

  // Enhanced Icon Button styling for maximum visibility
  MuiIconButton: {
    styleOverrides: {
      root: ({ theme }) => ({
        color: getIconContrastColor(theme, 'default', 'general'),
        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        borderRadius: '12px',
        padding: '8px',
        position: 'relative',

        '& .MuiSvgIcon-root': {
          filter: `brightness(1.1) contrast(1.1) drop-shadow(${getIconShadow(theme, 'medium')})`,
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        },

        '&:hover': {
          color: getIconContrastColor(theme, 'primary', 'general'),
          backgroundColor: alpha(getIconContrastColor(theme, 'primary', 'general'), 0.08),
          transform: 'scale(1.05)',

          '& .MuiSvgIcon-root': {
            filter: `brightness(1.2) contrast(1.2) drop-shadow(${getIconShadow(theme, 'strong')})`,
          },
        },

        '&:focus-visible': {
          outline: `2px solid ${getIconContrastColor(theme, 'primary', 'general')}`,
          outlineOffset: '2px',
        },

        '&.Mui-disabled': {
          color: alpha(getIconContrastColor(theme, 'muted', 'general'), 0.5),

          '& .MuiSvgIcon-root': {
            filter: `brightness(0.8) contrast(0.8) drop-shadow(${getIconShadow(theme, 'light')})`,
          },
        },
      }),
    },
  },

  // Enhanced SvgIcon styling for global icon improvements
  MuiSvgIcon: {
    styleOverrides: {
      root: ({ theme }) => ({
        color: 'inherit',
        filter: `brightness(1.05) contrast(1.05) drop-shadow(${getIconShadow(theme, 'light')})`,
        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',

        // Size variants with enhanced visibility
        '&.MuiSvgIcon-fontSizeSmall': {
          fontSize: '1.25rem',
          filter: `brightness(1.1) contrast(1.1) drop-shadow(${getIconShadow(theme, 'medium')})`,
        },

        '&.MuiSvgIcon-fontSizeLarge': {
          fontSize: '2.25rem',
          filter: `brightness(1.05) contrast(1.1) drop-shadow(${getIconShadow(theme, 'strong')})`,
        },

        // Color variants with enhanced contrast
        '&.MuiSvgIcon-colorPrimary': {
          color: getIconContrastColor(theme, 'primary', 'general'),
        },

        '&.MuiSvgIcon-colorSecondary': {
          color: getIconContrastColor(theme, 'secondary', 'general'),
        },

        '&.MuiSvgIcon-colorAction': {
          color: getIconContrastColor(theme, 'default', 'general'),
        },

        '&.MuiSvgIcon-colorError': {
          color: getIconContrastColor(theme, 'error', 'general'),
        },

        '&.MuiSvgIcon-colorWarning': {
          color: getIconContrastColor(theme, 'warning', 'general'),
        },

        '&.MuiSvgIcon-colorSuccess': {
          color: getIconContrastColor(theme, 'success', 'general'),
        },
      }),
    },
  },

  // Enhanced ListItemIcon for navigation and menus
  MuiListItemIcon: {
    styleOverrides: {
      root: ({ theme }) => ({
        color: getIconContrastColor(theme, 'default', 'navigation'),
        minWidth: '40px',

        '& .MuiSvgIcon-root': {
          fontSize: '1.25rem',
          filter: `brightness(1.1) contrast(1.1) drop-shadow(${getIconShadow(theme, 'medium')})`,
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        },

        '.MuiListItemButton-root:hover &': {
          color: getIconContrastColor(theme, 'primary', 'navigation'),

          '& .MuiSvgIcon-root': {
            filter: `brightness(1.2) contrast(1.2) drop-shadow(${getIconShadow(theme, 'strong')})`,
            transform: 'scale(1.1)',
          },
        },
      }),
    },
  },
};

// Create the main theme
const createAppTheme = (mode = 'light') => {
  const isLight = mode === 'light';
  
  return createTheme({
    palette: {
      mode,
      primary: {
        main: colors.primary[600],
        light: colors.primary[400],
        dark: colors.primary[800],
        contrastText: '#ffffff',
      },
      secondary: {
        main: colors.secondary[600],
        light: colors.secondary[400],
        dark: colors.secondary[800],
        contrastText: '#ffffff',
      },
      accent: {
        main: colors.accent[500],
        light: colors.accent[400],
        dark: colors.accent[700],
        contrastText: '#ffffff',
      },
      cyber: {
        main: colors.cyber[500],
        light: colors.cyber[400],
        dark: colors.cyber[700],
        contrastText: '#ffffff',
      },
      success: {
        main: colors.success[600],
        light: colors.success[400],
        dark: colors.success[800],
        contrastText: '#ffffff',
      },
      warning: {
        main: colors.warning[500],
        light: colors.warning[400],
        dark: colors.warning[700],
        contrastText: '#ffffff',
      },
      error: {
        main: colors.error[600],
        light: colors.error[400],
        dark: colors.error[800],
        contrastText: '#ffffff',
      },
      background: {
        default: isLight ? colors.gray[50] : colors.gray[900],
        paper: isLight ? '#ffffff' : colors.gray[800],
      },
      text: {
        primary: isLight ? colors.gray[900] : colors.gray[50],
        secondary: isLight ? colors.gray[600] : colors.gray[300],
      },
      divider: isLight ? colors.gray[200] : colors.gray[700],
    },
    typography,
    components,
    shape: {
      borderRadius: 12,
    },
    spacing: 8,
  });
};

export default createAppTheme;
export { colors };
