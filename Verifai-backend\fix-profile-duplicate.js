const { Client } = require('pg');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

async function fixProfileDuplicate() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        console.log('🔧 Fixing profile duplicate issue...\n');

        // Show current profiles for testmanu
        console.log('📋 CURRENT PROFILES FOR testmanu:');
        const profiles = await client.query('SELECT * FROM profile WHERE username = $1 ORDER BY id', ['testmanu']);
        profiles.rows.forEach((profile, index) => {
            console.log(`  Profile ${index + 1} (ID: ${profile.id}):`);
            console.log(`    Name: "${profile.name}"`);
            console.log(`    Description: "${profile.description}"`);
            console.log(`    Website: "${profile.website}"`);
            console.log(`    Location: "${profile.location}"`);
            console.log(`    Image: "${profile.image}"`);
            console.log(`    Role: "${profile.role}"`);
            console.log('');
        });

        if (profiles.rows.length > 1) {
            console.log('🗑️ Removing duplicate profile...');
            
            // Keep the first profile (ManuUTHM) and remove the test one
            const profileToKeep = profiles.rows[0]; // ManuUTHM profile
            const profileToRemove = profiles.rows[1]; // Test Manufacturer profile
            
            console.log(`Keeping profile: "${profileToKeep.name}" (ID: ${profileToKeep.id})`);
            console.log(`Removing profile: "${profileToRemove.name}" (ID: ${profileToRemove.id})`);
            
            // Delete the duplicate profile
            await client.query('DELETE FROM profile WHERE id = $1', [profileToRemove.id]);
            console.log('✅ Duplicate profile removed');
            
            // Update the remaining profile to have consistent information
            console.log('\n🔄 Updating remaining profile...');
            await client.query(`
                UPDATE profile 
                SET description = $1, 
                    website = $2, 
                    location = $3
                WHERE id = $4
            `, [
                'Manufacturer Profile', // Better description
                '', // Empty website
                'Malaysia', // Better location
                profileToKeep.id
            ]);
            console.log('✅ Profile updated');
        }

        // Verify the fix
        console.log('\n✅ VERIFICATION - FINAL PROFILE:');
        const finalProfile = await client.query('SELECT * FROM profile WHERE username = $1', ['testmanu']);
        if (finalProfile.rows.length === 1) {
            const profile = finalProfile.rows[0];
            console.log(`  Username: "${profile.username}"`);
            console.log(`  Name: "${profile.name}"`);
            console.log(`  Description: "${profile.description}"`);
            console.log(`  Website: "${profile.website}"`);
            console.log(`  Location: "${profile.location}"`);
            console.log(`  Image: "${profile.image}"`);
            console.log(`  Role: "${profile.role}"`);
        } else {
            console.log(`  Found ${finalProfile.rows.length} profiles (should be 1)`);
        }

        console.log('\n📝 SUMMARY:');
        console.log('  ✅ Username in auth table: "testmanu"');
        console.log('  ✅ Profile name: "ManuUTHM"');
        console.log('  ✅ Blockchain identity: "ManuUTHM"');
        console.log('  ✅ No more duplicates');
        
        console.log('\n💡 EXPLANATION:');
        console.log('  The "testaManu" you saw in the UI might be:');
        console.log('  1. A frontend display bug');
        console.log('  2. Cached data in browser');
        console.log('  3. Different data source in the UI');
        console.log('  4. JavaScript case conversion issue');
        
        console.log('\n🔧 NEXT STEPS:');
        console.log('  1. Clear browser cache/localStorage');
        console.log('  2. Logout and login again');
        console.log('  3. Check if "testaManu" still appears');
        console.log('  4. If it persists, we need to check frontend code');

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the fix
fixProfileDuplicate();
