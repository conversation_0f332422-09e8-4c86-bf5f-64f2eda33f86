const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Verifai Contract", function () {
  let verifai;

  beforeEach(async () => {
    const Verifai = await ethers.getContractFactory("Verifai");
    verifai = await Verifai.deploy();
    await verifai.deployed();
  });

  it("should register a product and return correct details", async function () {
    const serial = "123-XYZ";
    const name = "iPhone 15";
    const brand = "Apple";
    const description = "Brand new phone";
    const image = "image-url.com";
    const actor = "Factory ABC";
    const location = "Shenzhen";
    const timestamp = "2025-04-19 10:00AM";

    await verifai.registerProduct(name, brand, serial, description, image, actor, location, timestamp);

    const product = await verifai.getProduct(serial);

    // Verifying product main details
    expect(product[0]).to.equal(serial);
    expect(product[1]).to.equal(name);
    expect(product[2]).to.equal(brand);
    expect(product[3]).to.equal(description);
    expect(product[4]).to.equal(image);

    // Verifying history array
    const history = product[5]; // ProductHistory[]
    expect(history.length).to.equal(1);
    expect(history[0].actor).to.equal(actor);
    expect(history[0].location).to.equal(location);
    expect(history[0].timestamp).to.equal(timestamp);
    expect(history[0].isSold).to.equal(false);
  });
});
