<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Authentication - Verifai</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        h1 {
            color: #4a5568;
            margin-bottom: 20px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success { background: #c6f6d5; color: #22543d; }
        .info { background: #bee3f8; color: #2a4365; }
        .warning { background: #fefcbf; color: #744210; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Clear Authentication Issues</h1>
        <p>This tool will clear all authentication tokens and reset your session to fix login issues.</p>
        
        <div id="status"></div>
        
        <button onclick="clearAllAuth()">Clear All Authentication Data</button>
        <button onclick="clearBrowserCache()">Clear Browser Cache</button>
        <button onclick="resetEverything()">Reset Everything</button>
        
        <div style="margin-top: 30px;">
            <h3>Manual Steps:</h3>
            <ol style="text-align: left;">
                <li>Clear all authentication tokens</li>
                <li>Clear browser cache and cookies</li>
                <li>Reset MetaMask account (if needed)</li>
                <li>Restart development servers</li>
                <li>Try logging in again</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML += `<div class="status ${type}">${message}</div>`;
        }

        function clearAllAuth() {
            try {
                // Clear localStorage
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                        key.includes('token') || 
                        key.includes('auth') || 
                        key.includes('user') ||
                        key.includes('verifai') ||
                        key.includes('login')
                    )) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    log(`Removed: ${key}`, 'info');
                });

                // Clear sessionStorage
                const sessionKeysToRemove = [];
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && (
                        key.includes('token') || 
                        key.includes('auth') || 
                        key.includes('user') ||
                        key.includes('verifai') ||
                        key.includes('login')
                    )) {
                        sessionKeysToRemove.push(key);
                    }
                }
                
                sessionKeysToRemove.forEach(key => {
                    sessionStorage.removeItem(key);
                    log(`Removed from session: ${key}`, 'info');
                });

                log('✅ All authentication data cleared!', 'success');
                log('Please refresh the page and try logging in again.', 'info');
                
            } catch (error) {
                log(`❌ Error clearing auth data: ${error.message}`, 'warning');
            }
        }

        function clearBrowserCache() {
            try {
                // Clear all storage
                localStorage.clear();
                sessionStorage.clear();
                
                log('✅ Browser storage cleared!', 'success');
                log('Please close and reopen your browser, then try again.', 'info');
                
                // Try to clear cookies (limited by browser security)
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                log('✅ Cookies cleared!', 'success');
                
            } catch (error) {
                log(`❌ Error clearing cache: ${error.message}`, 'warning');
            }
        }

        function resetEverything() {
            clearAllAuth();
            clearBrowserCache();
            
            log('🔄 Complete reset performed!', 'success');
            log('Next steps:', 'info');
            log('1. Close this browser tab', 'info');
            log('2. Close all browser windows', 'info');
            log('3. Restart your browser', 'info');
            log('4. Go to http://localhost:5173', 'info');
            log('5. Try logging in again', 'info');
            
            setTimeout(() => {
                if (confirm('Would you like to close this tab and go to the login page?')) {
                    window.location.href = 'http://localhost:5173/login';
                }
            }, 2000);
        }

        // Auto-run basic check on page load
        window.onload = function() {
            log('🔍 Checking authentication data...', 'info');
            
            let authItems = 0;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('token') || key.includes('auth') || key.includes('verifai'))) {
                    authItems++;
                }
            }
            
            if (authItems > 0) {
                log(`Found ${authItems} authentication items in storage`, 'warning');
                log('Click "Clear All Authentication Data" to fix login issues', 'info');
            } else {
                log('No authentication data found in storage', 'success');
            }
        };
    </script>
</body>
</html>
