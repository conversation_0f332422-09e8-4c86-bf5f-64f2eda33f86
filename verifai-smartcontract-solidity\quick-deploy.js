const { ethers } = require("hardhat");

async function main() {
    console.log("🚀 Quick deployment to localhost...");

    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);

    const Verifai = await ethers.getContractFactory("Verifai");
    const verifai = await Verifai.deploy();
    await verifai.deployed();

    console.log("✅ Contract deployed at:", verifai.address);
    console.log("🔧 Update your frontend CONTRACT_ADDRESS to:", verifai.address);
}

main().catch(console.error);
