import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  useTheme,
  alpha,
  Stack,
  Chip
} from "@mui/material";
import { styled, keyframes } from "@mui/material/styles";
import {
  Security,
  QrCodeScanner,
  Timeline,
  Verified,
  Speed,
  Shield,
  TrendingUp,
  Fingerprint,
  CloudDone
} from "@mui/icons-material";

// Professional animations
const float = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
`;

const glow = keyframes`
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.6); }
`;

const sparkle = keyframes`
  0%, 100% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
`;

const orbitalMotion = keyframes`
  0% {
    transform: rotate(0deg) translateX(50px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(50px) rotate(-360deg);
  }
`;

// Professional styled components
const FeaturesContainer = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#f8fafc'} 0%,
    ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#e2e8f0'} 50%,
    ${theme.palette.mode === 'dark' ? '#16213e' : '#cbd5e1'} 100%
  )`,
  position: 'relative',
  overflow: 'hidden',
  py: 12,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 20%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 50%)
    `,
    pointerEvents: 'none',
  },
}));

const FeatureCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  borderRadius: '24px',
  padding: '32px',
  height: '100%',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  animation: `${float} 6s ease-in-out infinite`,
  boxShadow: `
    0 8px 32px ${alpha(theme.palette.common.black, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
  `,
  '&:hover': {
    transform: 'translateY(-12px)',
    boxShadow: `
      0 20px 40px ${alpha(theme.palette.common.black, 0.15)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.3)}
    `,
    '& .feature-icon': {
      animation: `${glow} 2s ease-in-out infinite`,
      transform: 'scale(1.1)',
    },
  },
}));

// Feature Particle Components
const FeatureParticle = styled(Box)(({ theme, size = 4, delay = 0, duration = 12, type = 'sparkle' }) => ({
  position: 'absolute',
  width: size,
  height: size,
  background: type === 'sparkle'
    ? `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
    : `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.6)} 0%, transparent 70%)`,
  borderRadius: type === 'sparkle' ? '2px' : '50%',
  animation: type === 'sparkle'
    ? `${sparkle} ${duration}s ease-in-out infinite`
    : `${orbitalMotion} ${duration}s linear infinite`,
  animationDelay: `${delay}s`,
  pointerEvents: 'none',
  zIndex: 1,
}));

const FeatureIcon = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  borderRadius: '20px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '24px',
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`,
  transition: 'all 0.3s ease',
}));

const Features = () => {
  const theme = useTheme();

  const features = [
    {
      icon: <Security sx={{ fontSize: 40, color: 'white' }} />,
      title: "Blockchain Security",
      description: "Immutable product records stored on blockchain technology ensuring tamper-proof authentication and complete transparency.",
      color: theme.palette.primary.main,
    },
    {
      icon: <QrCodeScanner sx={{ fontSize: 40, color: 'white' }} />,
      title: "Instant Verification",
      description: "Quick QR code scanning provides immediate product authentication results with detailed history and ownership tracking.",
      color: theme.palette.secondary.main,
    },
    {
      icon: <Timeline sx={{ fontSize: 40, color: 'white' }} />,
      title: "Supply Chain Tracking",
      description: "Complete product journey visibility from manufacturer to consumer with real-time updates and location tracking.",
      color: theme.palette.success.main,
    },
    {
      icon: <Verified sx={{ fontSize: 40, color: 'white' }} />,
      title: "Anti-Counterfeiting",
      description: "Advanced algorithms detect fake products and protect consumers from counterfeit goods with 99.9% accuracy.",
      color: theme.palette.info.main,
    },
    {
      icon: <Speed sx={{ fontSize: 40, color: 'white' }} />,
      title: "Real-Time Updates",
      description: "Live product status updates and instant notifications for any changes in the supply chain or ownership.",
      color: theme.palette.warning.main,
    },
    {
      icon: <Shield sx={{ fontSize: 40, color: 'white' }} />,
      title: "Enterprise Security",
      description: "Bank-grade encryption and security protocols protect sensitive product data and user information.",
      color: theme.palette.error.main,
    },
  ];

  const stats = [
    { number: "99.9%", label: "Accuracy Rate", icon: <TrendingUp /> },
    { number: "10K+", label: "Products Verified", icon: <Verified /> },
    { number: "500+", label: "Trusted Partners", icon: <Fingerprint /> },
    { number: "24/7", label: "System Uptime", icon: <CloudDone /> },
  ];

  return (
    <FeaturesContainer>
      {/* Feature Section Particle Effects */}
      {Array.from({ length: 8 }).map((_, i) => (
        <FeatureParticle
          key={`sparkle-${i}`}
          size={Math.random() * 6 + 2}
          delay={Math.random() * 5}
          duration={Math.random() * 8 + 10}
          type="sparkle"
          sx={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
        />
      ))}

      {/* Orbital Particles */}
      {Array.from({ length: 4 }).map((_, i) => (
        <FeatureParticle
          key={`orbital-${i}`}
          size={Math.random() * 8 + 4}
          delay={i * 3}
          duration={Math.random() * 15 + 20}
          type="orbital"
          sx={{
            top: `${30 + Math.random() * 40}%`,
            left: `${20 + Math.random() * 60}%`,
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Header Section */}
        <Box sx={{ textAlign: 'center', mb: 8 }}>
          <Chip
            label="🚀 Powered by Blockchain Technology"
            variant="outlined"
            sx={{
              mb: 3,
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              fontWeight: 600,
              fontSize: '1rem',
              px: 2,
              py: 1,
            }}
          />
          
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              fontWeight: 800,
              mb: 3,
              background: `linear-gradient(135deg,
                ${theme.palette.primary.main} 0%,
                ${theme.palette.secondary.main} 50%,
                ${theme.palette.primary.dark} 100%
              )`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              letterSpacing: '-0.02em',
            }}
          >
            How Verifai Works
          </Typography>
          
          <Typography
            variant="h6"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: '800px',
              mx: 'auto',
              lineHeight: 1.6,
              fontSize: '1.2rem',
              fontWeight: 500,
            }}
          >
            Our revolutionary blockchain-powered platform provides unmatched product authentication 
            and supply chain transparency, protecting consumers and businesses from counterfeit products.
          </Typography>
        </Box>

        {/* Features Grid */}
        <Grid container spacing={4} sx={{ mb: 8 }}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <FeatureCard>
                <FeatureIcon 
                  className="feature-icon"
                  sx={{
                    background: `linear-gradient(135deg, ${feature.color} 0%, ${alpha(feature.color, 0.8)} 100%)`,
                  }}
                >
                  {feature.icon}
                </FeatureIcon>
                <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
                  {feature.title}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  {feature.description}
                </Typography>
              </FeatureCard>
            </Grid>
          ))}
        </Grid>

        {/* Stats Section */}
        <Box sx={{ 
          background: `linear-gradient(135deg,
            ${alpha(theme.palette.primary.main, 0.1)} 0%,
            ${alpha(theme.palette.secondary.main, 0.1)} 100%
          )`,
          borderRadius: '24px',
          p: 6,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
        }}>
          <Typography variant="h4" sx={{ textAlign: 'center', fontWeight: 700, mb: 4 }}>
            Trusted by Industry Leaders
          </Typography>
          <Grid container spacing={4}>
            {stats.map((stat, index) => (
              <Grid item xs={6} md={3} key={index}>
                <Box sx={{ textAlign: 'center' }}>
                  <Box sx={{ 
                    color: theme.palette.primary.main, 
                    mb: 1,
                    display: 'flex',
                    justifyContent: 'center'
                  }}>
                    {stat.icon}
                  </Box>
                  <Typography variant="h3" sx={{ fontWeight: 800, color: theme.palette.primary.main, mb: 1 }}>
                    {stat.number}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                    {stat.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    </FeaturesContainer>
  );
};

export default Features;
