import React from 'react';
import { Box, alpha, useTheme } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

// Professional Animation Keyframes
const elegantFloat = keyframes`
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.6;
    }
    25% { 
        transform: translateY(-20px) rotate(90deg); 
        opacity: 0.8;
    }
    50% { 
        transform: translateY(-10px) rotate(180deg); 
        opacity: 1;
    }
    75% { 
        transform: translateY(-30px) rotate(270deg); 
        opacity: 0.8;
    }
`;

const gentleWave = keyframes`
    0%, 100% { 
        transform: translateX(0px) scaleY(1); 
        opacity: 0.3;
    }
    50% { 
        transform: translateX(50px) scaleY(1.1); 
        opacity: 0.6;
    }
`;

const subtlePulse = keyframes`
    0%, 100% { 
        transform: scale(1); 
        opacity: 0.4;
    }
    50% { 
        transform: scale(1.05); 
        opacity: 0.7;
    }
`;

const flowingGradient = keyframes`
    0% { 
        background-position: 0% 50%; 
    }
    50% { 
        background-position: 100% 50%; 
    }
    100% { 
        background-position: 0% 50%; 
    }
`;

const shimmerEffect = keyframes`
    0% { 
        transform: translateX(-100%); 
        opacity: 0;
    }
    50% { 
        opacity: 1;
    }
    100% { 
        transform: translateX(100%); 
        opacity: 0;
    }
`;

// Styled Animation Components
const AnimatedOrb = styled(Box)(({ theme, size = 100, delay = 0, color = 'primary' }) => ({
    position: 'absolute',
    width: size,
    height: size,
    borderRadius: '50%',
    background: `
        radial-gradient(circle at 30% 30%, 
            ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.15)} 0%, 
            ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.05)} 50%,
            transparent 100%
        )
    `,
    border: `1px solid ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.1)}`,
    animation: `${elegantFloat} 20s ease-in-out infinite`,
    animationDelay: `${delay}s`,
    filter: 'blur(1px)',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: '20%',
        left: '20%',
        width: '60%',
        height: '60%',
        borderRadius: '50%',
        background: `radial-gradient(circle, ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.2)} 0%, transparent 70%)`,
        animation: `${subtlePulse} 8s ease-in-out infinite`,
        animationDelay: `${delay * 0.5}s`,
    },
}));

const FlowingLine = styled(Box)(({ theme, width = 200, delay = 0 }) => ({
    position: 'absolute',
    width: width,
    height: '2px',
    background: `
        linear-gradient(90deg, 
            transparent 0%, 
            ${alpha(theme.palette.primary.main, 0.3)} 20%,
            ${alpha(theme.palette.primary.main, 0.6)} 50%,
            ${alpha(theme.palette.primary.main, 0.3)} 80%,
            transparent 100%
        )
    `,
    backgroundSize: '200% 100%',
    animation: `${flowingGradient} 15s ease-in-out infinite, ${gentleWave} 25s ease-in-out infinite`,
    animationDelay: `${delay}s`,
    filter: 'blur(0.5px)',
    '&::after': {
        content: '""',
        position: 'absolute',
        top: '-1px',
        left: 0,
        width: '100%',
        height: '4px',
        background: 'inherit',
        filter: 'blur(2px)',
        opacity: 0.5,
    },
}));

const GeometricShape = styled(Box)(({ theme, size = 60, delay = 0, shape = 'diamond' }) => ({
    position: 'absolute',
    width: size,
    height: size,
    background: `linear-gradient(135deg, 
        ${alpha(theme.palette.secondary.main, 0.1)} 0%, 
        ${alpha(theme.palette.secondary.main, 0.05)} 100%
    )`,
    border: `1px solid ${alpha(theme.palette.secondary.main, 0.15)}`,
    transform: shape === 'diamond' ? 'rotate(45deg)' : 'rotate(0deg)',
    borderRadius: shape === 'circle' ? '50%' : shape === 'rounded' ? '20%' : '0%',
    animation: `${elegantFloat} 30s ease-in-out infinite`,
    animationDelay: `${delay}s`,
    filter: 'blur(0.5px)',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: '25%',
        left: '25%',
        width: '50%',
        height: '50%',
        background: `radial-gradient(circle, ${alpha(theme.palette.secondary.main, 0.2)} 0%, transparent 70%)`,
        borderRadius: 'inherit',
        animation: `${subtlePulse} 12s ease-in-out infinite`,
        animationDelay: `${delay * 0.3}s`,
    },
}));

const ShimmerLine = styled(Box)(({ theme, delay = 0 }) => ({
    position: 'absolute',
    width: '100%',
    height: '1px',
    background: `linear-gradient(90deg, 
        transparent 0%, 
        ${alpha(theme.palette.primary.main, 0.4)} 50%, 
        transparent 100%
    )`,
    animation: `${shimmerEffect} 8s ease-in-out infinite`,
    animationDelay: `${delay}s`,
}));

// Main Component
const ElegantBackground = ({ 
    variant = 'dashboard', // 'dashboard', 'scanner', 'minimal'
    intensity = 'medium' // 'low', 'medium', 'high'
}) => {
    const theme = useTheme();

    const getElementCount = () => {
        switch (intensity) {
            case 'low': return { orbs: 3, lines: 2, shapes: 2 };
            case 'high': return { orbs: 8, lines: 6, shapes: 6 };
            default: return { orbs: 5, lines: 4, shapes: 4 };
        }
    };

    const { orbs, lines, shapes } = getElementCount();

    return (
        <Box sx={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            width: '100%', 
            height: '100%', 
            zIndex: 1, 
            pointerEvents: 'none',
            overflow: 'hidden'
        }}>
            {/* Floating Orbs */}
            {Array.from({ length: orbs }).map((_, i) => (
                <AnimatedOrb 
                    key={`orb-${i}`}
                    size={60 + Math.random() * 60} 
                    delay={i * 3} 
                    color={i % 2 === 0 ? 'primary' : 'secondary'}
                    sx={{ 
                        top: `${10 + Math.random() * 70}%`, 
                        left: `${5 + Math.random() * 85}%` 
                    }}
                />
            ))}

            {/* Flowing Lines */}
            {Array.from({ length: lines }).map((_, i) => (
                <FlowingLine 
                    key={`line-${i}`}
                    width={150 + Math.random() * 200} 
                    delay={i * 2}
                    sx={{ 
                        top: `${20 + Math.random() * 60}%`, 
                        left: `${10 + Math.random() * 70}%`,
                        transform: `rotate(${-30 + Math.random() * 60}deg)`
                    }}
                />
            ))}

            {/* Geometric Shapes */}
            {Array.from({ length: shapes }).map((_, i) => {
                const shapeTypes = ['diamond', 'circle', 'rounded'];
                return (
                    <GeometricShape 
                        key={`shape-${i}`}
                        size={30 + Math.random() * 40} 
                        delay={i * 4} 
                        shape={shapeTypes[i % shapeTypes.length]}
                        sx={{ 
                            top: `${15 + Math.random() * 70}%`, 
                            left: `${15 + Math.random() * 70}%` 
                        }}
                    />
                );
            })}

            {/* Shimmer Effects for Scanner variant */}
            {variant === 'scanner' && (
                <>
                    <ShimmerLine delay={0} sx={{ top: '20%' }} />
                    <ShimmerLine delay={3} sx={{ top: '50%' }} />
                    <ShimmerLine delay={6} sx={{ top: '80%' }} />
                </>
            )}
        </Box>
    );
};

export default ElegantBackground;
