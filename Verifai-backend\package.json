{"dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@rollup/plugin-terser": "^0.4.4", "@svgr/plugin-svgo": "^8.1.0", "are-we-there-yet": "^4.0.2", "axios": "^1.10.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "gauge": "^5.0.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "npmlog": "^7.0.1", "nth-check": "^2.1.1", "pg": "^8.13.3", "postcss": "^8.4.31", "react-scripts": "^5.0.1", "resolve-url-loader": "^5.0.0", "rimraf": "^6.0.1", "serialize-javascript": "^6.0.2", "svgo": "^3.3.2", "workbox-webpack-plugin": "^7.3.0"}, "overrides": {"nth-check": "^2.1.1"}}