import {
    Box,
    Paper,
    Avatar,
    Typography,
    Button,
    Container,
    Card,
    CardContent,
    Chip,
    LinearProgress,
    Alert,
    Snackbar,
    Fade,
    Zoom,
    useTheme,
    alpha,
    IconButton,
    Tooltip,
    Stack,
    Divider,
    Grid
} from '@mui/material';
import {
    Timeline,
    TimelineItem,
    TimelineSeparator,
    TimelineConnector,
    TimelineContent,
    TimelineDot,
    TimelineOppositeContent,
    timelineOppositeContentClasses
} from '@mui/lab';
import {
    Update,
    LocationOn,
    Person,
    CalendarToday,
    CheckCircle,
    Cancel,
    Link,
    Speed,
    AutoAwesome,
    ArrowBack,
    Fingerprint,
    Schedule,
    Inventory,
    QrCode,
    Timeline as TimelineIcon,
    AccountBalanceWallet,
    Verified,
    LocalShipping,
    Store,
    History,
    Info,
    Visibility
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';
import dayjs from 'dayjs';
import { useLocation, useNavigate } from 'react-router-dom';
import abi from '../../utils/Verifai.json';
import { useEffect, useState } from 'react';
import useAuth from '../../hooks/useAuth';
import { BrowserProvider, Contract } from "ethers";
import { apiGet } from '../../utils/apiUtils';


// Animations
const glow = keyframes`
  0%, 100% { box-shadow: 0 0 20px rgba(46, 125, 50, 0.3); }
  50% { box-shadow: 0 0 40px rgba(46, 125, 50, 0.6); }
`;

const float = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

// Styled Components
const FuturisticContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg,
    ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#f8fafc'} 0%,
    ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#e2e8f0'} 50%,
    ${theme.palette.mode === 'dark' ? '#16213e' : '#cbd5e1'} 100%
  )`,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 50%),
                 radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 50%)`,
    pointerEvents: 'none',
  },
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '24px',
  boxShadow: `0 20px 40px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '2px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  },
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 32px 64px ${alpha(theme.palette.common.black, 0.15)}`,
  },
}));

const InfoCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.8)} 0%,
    ${alpha(theme.palette.background.paper, 0.6)} 100%
  )`,
  backdropFilter: 'blur(15px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '20px',
  padding: '24px',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const ProductAvatar = styled(Avatar)(({ theme }) => ({
  width: 120,
  height: 120,
  border: `4px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  boxShadow: `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`,
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  animation: `${float} 6s ease-in-out infinite`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'scale(1.1)',
    boxShadow: `0 16px 45px ${alpha(theme.palette.primary.main, 0.6)}`,
  },
}));

const CyberButton = styled(Button)(({ theme, variant: buttonVariant }) => ({
  borderRadius: '12px',
  padding: '12px 32px',
  fontWeight: 600,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  background: buttonVariant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: buttonVariant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: buttonVariant === 'primary' ? 'none' : `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  boxShadow: buttonVariant === 'primary'
    ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 12px ${alpha(theme.palette.common.black, 0.1)}`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: buttonVariant === 'primary'
      ? `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 20px ${alpha(theme.palette.common.black, 0.15)}`,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0px)',
  },
}));

const getEthereumObject = () => window.ethereum;

/*
 * This function returns the first linked account found.
 * If there is no account linked, it will return null.
 */
const findMetaMaskAccount = async () => {
    try {
        const ethereum = getEthereumObject();

        /*
        * First make sure we have access to the Ethereum object.
        */
        if (!ethereum) {
            console.error("Make sure you have Metamask!");
            return null;
        }

        console.log("We have the Ethereum object", ethereum);
        const accounts = await ethereum.request({ method: "eth_accounts" });

        if (accounts.length !== 0) {
            const account = accounts[0];
            console.log("Found an authorized account:", account);
            return account;
        } else {
            console.error("No authorized account found");
            return null;
        }
    } catch (error) {
        console.error(error);
        return null;
    }
};



const UpdateProduct = () => {
    const [currentAccount, setCurrentAccount] = useState("");
    const [suppDate, setSuppDate] = useState('');
    const [suppLatitude, setSuppLatitude] = useState("");
    const [suppLongtitude, setSuppLongtitude] = useState("");
    const [suppName, setSuppName] = useState("");
    const [suppLocation, setSuppLocation] = useState("");
    const [loading, setLoading] = useState("");
    const [serialNumber, setSerialNumber] = useState("");
    const [productData, setProductData] = useState("");

    const [name, setName] = useState("");
    const [brand, setBrand] = useState("");
    const [description, setDescription] = useState("");
    const [imageName, setImageName] = useState("");
    const [history, setHistory] = useState([]);
    const [isSold, setIsSold] = useState(false);

    const [image, setImage] = useState({
        file: [],
        filepreview: null
    });

    const [transactionStatus, setTransactionStatus] = useState({
        open: false,
        message: '',
        severity: 'info'
    });




    const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || "******************************************";
    const CONTRACT_ABI = abi.abi;

    const { auth, user, isAuthenticated, isLoading } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();
    const qrData = location.state?.qrData;

    console.log("qrData", qrData);
    console.log("auth state", { auth, user, isAuthenticated });

    useEffect(() => {
        // Check authentication first
        if (isLoading) {
            console.log('Authentication still loading...');
            return;
        }

        if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            navigate('/login');
            return;
        }

        // Check if user has permission to update products
        const userRole = auth?.user?.role || user?.role;
        if (!['supplier', 'retailer', 'admin'].includes(userRole)) {
            console.log('User does not have permission to update products');
            navigate('/');
            return;
        }

        console.log("useEffect - initializing with role:", userRole);

        findMetaMaskAccount().then((account) => {
            if (account !== null) {
                setCurrentAccount(account);
            }
        });

        if (qrData) {
            handleScan(qrData);
        }

    }, [qrData, isLoading, isAuthenticated, navigate, auth, user]);

    const getImage = async (imageName) => {
        setImage(prevState => ({
            ...prevState,
            filepreview: `${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/file/product/${imageName}`
            })
        )
    }

    const handleScan = async (qrData) => {
        const data = qrData.split(",");
        const contractAddress = data[0];
        setSerialNumber(data[1]);

        console.log("contract address", contractAddress)
        console.log("serial number", data[1])

        if (contractAddress === CONTRACT_ADDRESS) {

            try {
                const { ethereum } = window;

                if (ethereum) {
                    // Request account access if needed
                    await ethereum.request({ method: 'eth_requestAccounts' });

                    const provider = new BrowserProvider(ethereum);
                    const signer = await provider.getSigner();
                    const productContract = new Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

                    console.log("Attempting to get product with serial number:", data[1]);

                    try {
                        const product = await productContract.getProduct(data[1].toString());

                        console.log("Retrieved product...", product);

                        // Check if product exists (not empty)
                        if (!product || product.length === 0 || !product[0] || product[0] === "") {
                            console.log("Product not found or empty data returned");
                            setTransactionStatus({
                                open: true,
                                message: `Product with serial number "${data[1]}" not found in the blockchain. Please verify the QR code is correct.`,
                                severity: 'warning'
                            });
                            return;
                        }

                        console.log("Product structure:", {
                            serialNumber: product[0],
                            name: product[1],
                            brand: product[2],
                            description: product[3],
                            image: product[4],
                            history: product[5]
                        });

                        // Parse the structured data instead of converting to string
                        setStructuredData(product);

                        setTransactionStatus({
                            open: true,
                            message: 'Product successfully retrieved from blockchain!',
                            severity: 'success'
                        });

                    } catch (contractError) {
                        console.error("Contract call error:", contractError);

                        if (contractError.message.includes("could not decode result data")) {
                            setTransactionStatus({
                                open: true,
                                message: `Product with serial number "${data[1]}" does not exist in the blockchain. Please verify the QR code is from a registered product. Click the diagnostic button (🛡️) in the bottom-right corner to troubleshoot.`,
                                severity: 'error'
                            });
                        } else if (contractError.message.includes("user rejected")) {
                            setTransactionStatus({
                                open: true,
                                message: 'Transaction was rejected by user.',
                                severity: 'info'
                            });
                        } else {
                            setTransactionStatus({
                                open: true,
                                message: `Error retrieving product: ${contractError.message}`,
                                severity: 'error'
                            });
                        }
                    }

                } else {
                    console.log("Ethereum object doesn't exist!");
                    setTransactionStatus({
                        open: true,
                        message: 'MetaMask not detected. Please install MetaMask extension to continue.',
                        severity: 'error'
                    });
                }
            } catch (error) {
                console.error("General error:", error);
                setTransactionStatus({
                    open: true,
                    message: `Unexpected error: ${error.message}`,
                    severity: 'error'
                });
            }
        }

    };

    // New function to handle structured data from smart contract
    const setStructuredData = (product) => {
        console.log("Setting structured product data:", product);

        // Extract basic product info
        setSerialNumber(product[0]);
        setName(product[1]);
        setBrand(product[2]);
        setDescription(product[3].replace(/;/g, ","));
        getImage(product[4]);

        // Extract and process history array
        const historyArray = product[5]; // This is the ProductHistory[] array
        console.log("Raw history array:", historyArray);

        const hist = [];
        for (let i = 0; i < historyArray.length; i++) {
            const historyEntry = historyArray[i];
            console.log("Processing history entry:", historyEntry);

            const actor = historyEntry.actor;
            const location = historyEntry.location.replace(/;/g, ",");
            const timestamp = historyEntry.timestamp;
            const isSold = historyEntry.isSold; // This is already a boolean from the smart contract

            console.log("History entry details:", {
                actor,
                location,
                timestamp,
                isSold,
                isSoldType: typeof isSold
            });

            hist.push({
                actor, location, timestamp, isSold
            });
        }

        console.log("Processed history:", hist);
        setHistory(hist);

        // Set the overall product's isSold status based on the latest history entry
        if (hist.length > 0) {
            const latestEntry = hist[hist.length - 1];
            setIsSold(latestEntry.isSold);
            console.log("Latest history entry:", latestEntry);
            console.log("Product isSold status:", latestEntry.isSold);
            console.log("Product isSold type:", typeof latestEntry.isSold);
        }
    }

    // Keep the old setData function for backward compatibility (if needed)
    const setData = (d) => {
        console.log("product data: ", d);

        const arr = d.split(",");
        console.log("arr", arr)

        setName(arr[1]);
        setBrand(arr[2]);
        setDescription(arr[3].replace(/;/g, ","));
        // setImageName(arr[4]);
        getImage(arr[4]);

        const hist = [];
        let start = 5;

        for (let i = 5; i < arr.length; i += 5) {
            const actor = arr[start + 1];
            const location = arr[start + 2].replace(/;/g, ",");
            const timestamp = arr[start + 3];
            const isSold = arr[start + 4] === "true";
            console.log("Parsing history entry - isSold string:", arr[start + 4], "converted to boolean:", isSold);

            hist.push({
                actor, location, timestamp, isSold
            });

            start += 5;
        }
        console.log("hist", hist)
        setHistory(hist);

        // Set the overall product's isSold status based on the latest history entry
        if (hist.length > 0) {
            const latestEntry = hist[hist.length - 1];
            setIsSold(latestEntry.isSold);
            console.log("Latest history entry:", latestEntry);
            console.log("Product isSold status:", latestEntry.isSold);
            console.log("Product isSold type:", typeof latestEntry.isSold);
        }

    }

    

    const handleBack = () => {
        navigate(-1)
    }


    const getHistory = () => {
        const theme = useTheme();

        return history.map((item, index) => {
            const date = dayjs(item.timestamp * 1000).format('MMM DD, YYYY');
            const time = dayjs(item.timestamp * 1000).format('HH:mm a');

            return (
                <TimelineItem key={index}>
                    <TimelineOppositeContent
                        color="textSecondary"
                        sx={{
                            fontWeight: 600,
                            fontSize: '0.9rem',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-end'
                        }}
                    >
                        <Typography variant="body2" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                            {time}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                            {date}
                        </Typography>
                    </TimelineOppositeContent>
                    <TimelineSeparator>
                        <TimelineDot sx={{
                            background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.primary.main} 100%)`,
                            border: 'none',
                            boxShadow: `0 4px 12px ${alpha(theme.palette.secondary.main, 0.3)}`,
                        }}>
                            <LocalShipping sx={{ fontSize: 16, color: 'white' }} />
                        </TimelineDot>
                        <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent sx={{ py: '12px', px: 2 }}>
                        <InfoCard sx={{ p: 2, mb: 0 }}>
                            <Stack spacing={1}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <LocationOn sx={{ color: theme.palette.success.main, fontSize: 18 }} />
                                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                        Location:
                                    </Typography>
                                    <Typography variant="body2">
                                        {item.location}
                                    </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Person sx={{ color: theme.palette.info.main, fontSize: 18 }} />
                                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                        Actor:
                                    </Typography>
                                    <Typography variant="body2">
                                        {item.actor}
                                    </Typography>
                                </Box>
                            </Stack>
                        </InfoCard>
                    </TimelineContent>
                </TimelineItem>
            );
        });
    }

    const handleSubmit = async (e) => {
        e.preventDefault();;

        navigate('/update-product-details', { state: { qrData }});
    }

    const handleCloseSnackbar = () => {
        setTransactionStatus(prev => ({ ...prev, open: false }));
    };
    

    const theme = useTheme();

    return (
        <FuturisticContainer>
            <Container maxWidth="lg" sx={{ py: 4, position: 'relative', zIndex: 2 }}>
                <Fade in timeout={800}>
                    <Box>
                        {/* Header Section */}
                        <Box sx={{ textAlign: 'center', mb: 6 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                                <IconButton
                                    onClick={handleBack}
                                    sx={{
                                        mr: 3,
                                        background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
                                        backdropFilter: 'blur(10px)',
                                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                        '&:hover': {
                                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                                            transform: 'translateY(-2px)',
                                        }
                                    }}
                                >
                                    <ArrowBack />
                                </IconButton>
                                <Inventory sx={{ fontSize: 48, color: theme.palette.primary.main, mr: 2 }} />
                                <Typography
                                    variant="h2"
                                    sx={{
                                        background: `linear-gradient(135deg,
                                            ${theme.palette.primary.main} 0%,
                                            ${theme.palette.secondary.main} 50%,
                                            ${theme.palette.primary.dark} 100%
                                        )`,
                                        backgroundClip: 'text',
                                        WebkitBackgroundClip: 'text',
                                        WebkitTextFillColor: 'transparent',
                                        fontWeight: 700,
                                        fontSize: { xs: '2.5rem', md: '3.5rem' },
                                        letterSpacing: '-0.02em',
                                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                    }}
                                >
                                    Product Details
                                </Typography>
                            </Box>
                            <Typography
                                variant="body1"
                                sx={{
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                    color: theme.palette.mode === 'dark' ? '#e2e8f0' : '#475569',
                                    fontWeight: 500,
                                    fontSize: '1.1rem',
                                    letterSpacing: '0.05em',
                                    mb: 2,
                                    position: 'relative',
                                    '&::before': {
                                        content: '">"',
                                        color: theme.palette.primary.main,
                                        marginRight: '8px',
                                        fontWeight: 700,
                                    },
                                }}
                            >
                                Blockchain Product Verification System
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500, opacity: 0.8 }}>
                                Secure product authentication and supply chain tracking
                            </Typography>
                        </Box>

                        {/* Product Information Section */}
                        <Grid container spacing={4} sx={{ mb: 6 }}>
                            {/* Product Overview Card */}
                            <Grid item xs={12} lg={8}>
                                <GlassCard sx={{ height: '100%' }}>
                                    <CardContent sx={{ p: 4 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                            <Inventory sx={{ color: theme.palette.primary.main, mr: 2, fontSize: 32 }} />
                                            <Typography variant="h4" sx={{ fontWeight: 700 }}>
                                                Product Overview
                                            </Typography>
                                        </Box>

                                        <Stack spacing={3}>
                                            {/* Product Name */}
                                            <InfoCard>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Verified sx={{ color: theme.palette.success.main, mr: 2 }} />
                                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                                        Product Name
                                                    </Typography>
                                                </Box>
                                                <Typography variant="h5" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
                                                    {name || 'Loading...'}
                                                </Typography>
                                            </InfoCard>

                                            {/* Serial Number */}
                                            <InfoCard>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Fingerprint sx={{ color: theme.palette.secondary.main, mr: 2 }} />
                                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                                        Serial Number
                                                    </Typography>
                                                </Box>
                                                <Typography variant="body1" sx={{ fontFamily: 'monospace', fontSize: '1.1rem', fontWeight: 600 }}>
                                                    #{serialNumber || 'Loading...'}
                                                </Typography>
                                            </InfoCard>

                                            {/* Description */}
                                            <InfoCard>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Info sx={{ color: theme.palette.info.main, mr: 2 }} />
                                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                                        Description
                                                    </Typography>
                                                </Box>
                                                <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                                                    {description || 'No description available'}
                                                </Typography>
                                            </InfoCard>

                                            {/* Brand */}
                                            <InfoCard>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Store sx={{ color: theme.palette.warning.main, mr: 2 }} />
                                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                                        Brand
                                                    </Typography>
                                                </Box>
                                                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                                    {brand || 'Unknown Brand'}
                                                </Typography>
                                            </InfoCard>
                                        </Stack>
                                    </CardContent>
                                </GlassCard>
                            </Grid>

                            {/* Product Image Card */}
                            <Grid item xs={12} lg={4}>
                                <GlassCard sx={{ height: '100%' }}>
                                    <CardContent sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                                            <Visibility sx={{ color: theme.palette.primary.main, mr: 2 }} />
                                            <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                                Product Image
                                            </Typography>
                                        </Box>

                                        <ProductAvatar
                                            alt={name}
                                            src={image.filepreview}
                                            sx={{ mx: 'auto', mb: 3 }}
                                        >
                                            {name ? name.charAt(0).toUpperCase() : 'P'}
                                        </ProductAvatar>

                                        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                                            Authenticated Product Image
                                        </Typography>

                                        <Chip
                                            label="Verified"
                                            color="success"
                                            icon={<CheckCircle />}
                                            sx={{
                                                mt: 2,
                                                fontWeight: 600,
                                                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                                color: 'white'
                                            }}
                                        />
                                    </CardContent>
                                </GlassCard>
                            </Grid>
                        </Grid>

                        {/* Supply Chain Timeline */}
                        <GlassCard sx={{ mb: 4 }}>
                            <CardContent sx={{ p: 4 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                                    <TimelineIcon sx={{ color: theme.palette.primary.main, mr: 2, fontSize: 32 }} />
                                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                                        Supply Chain History
                                    </Typography>
                                </Box>

                                <Timeline
                                    sx={{
                                        [`& .${timelineOppositeContentClasses.root}`]: {
                                            flex: 0.3,
                                        },
                                        '& .MuiTimelineItem-root': {
                                            '&:before': {
                                                display: 'none',
                                            },
                                        },
                                        '& .MuiTimelineDot-root': {
                                            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                                            border: 'none',
                                            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                                        },
                                        '& .MuiTimelineConnector-root': {
                                            background: `linear-gradient(180deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                                        },
                                    }}
                                >
                                    {getHistory()}

                                    {/* Current Status */}
                                    <TimelineItem>
                                        <TimelineOppositeContent color="textSecondary" sx={{ fontWeight: 600 }}>
                                            {dayjs().format('HH:mm a')} {dayjs().format('MM/DD/YYYY')}
                                        </TimelineOppositeContent>
                                        <TimelineSeparator>
                                            <TimelineDot sx={{
                                                background: isSold
                                                    ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
                                                    : `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                                                animation: `${pulse} 2s ease-in-out infinite`
                                            }}>
                                                {isSold ? <CheckCircle /> : <Schedule />}
                                            </TimelineDot>
                                        </TimelineSeparator>
                                        <TimelineContent sx={{ py: '12px', px: 2 }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                                    Current Status:
                                                </Typography>
                                                <Chip
                                                    label={isSold ? "Sold" : "Available"}
                                                    color={isSold ? "success" : "warning"}
                                                    icon={isSold ? <CheckCircle /> : <Schedule />}
                                                    sx={{
                                                        fontWeight: 600,
                                                        background: isSold
                                                            ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
                                                            : `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                                                        color: 'white'
                                                    }}
                                                />
                                            </Box>
                                        </TimelineContent>
                                    </TimelineItem>
                                </Timeline>
                            </CardContent>
                        </GlassCard>

                        {/* Loading Status */}
                        {loading && (
                            <Zoom in timeout={500}>
                                <GlassCard sx={{ textAlign: 'center', mb: 4 }}>
                                    <CardContent sx={{ p: 4 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                            <Security sx={{ color: theme.palette.primary.main, mr: 2 }} />
                                            <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                                Processing Request
                                            </Typography>
                                        </Box>
                                        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                                            {loading}
                                        </Typography>
                                        <LinearProgress
                                            sx={{
                                                borderRadius: 2,
                                                height: 8,
                                                background: alpha(theme.palette.primary.main, 0.1),
                                                '& .MuiLinearProgress-bar': {
                                                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                                                    borderRadius: 2,
                                                }
                                            }}
                                        />
                                    </CardContent>
                                </GlassCard>
                            </Zoom>
                        )}

                        {/* Action Buttons */}
                        <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', mt: 4 }}>
                            <CyberButton
                                variant="primary"
                                onClick={handleSubmit}
                                disabled={!!loading}
                                startIcon={<Update />}
                                sx={{
                                    minWidth: 220,
                                    py: 2,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                    textTransform: 'none',
                                }}
                            >
                                {loading ? "Processing..." : "Update Product"}
                            </CyberButton>

                            <CyberButton
                                variant="secondary"
                                onClick={handleBack}
                                startIcon={<ArrowBack />}
                                sx={{
                                    minWidth: 160,
                                    py: 2,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                    textTransform: 'none',
                                }}
                            >
                                Back
                            </CyberButton>
                        </Box>

                        {/* Footer */}
                        <Divider sx={{ my: 4 }} />
                        <Box sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                                Secure Blockchain Product Management • Powered by Verifai
                            </Typography>
                        </Box>

                        {/* Snackbar for notifications */}
                        <Snackbar
                            open={transactionStatus.open}
                            autoHideDuration={6000}
                            onClose={handleCloseSnackbar}
                            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                        >
                            <Alert
                                onClose={handleCloseSnackbar}
                                severity={transactionStatus.severity}
                                sx={{
                                    width: '100%',
                                    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
                                    backdropFilter: 'blur(20px)',
                                    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                                    borderRadius: '12px',
                                }}
                            >
                                {transactionStatus.message}
                            </Alert>
                        </Snackbar>

                        {/* Diagnostic Tool Button */}

                    </Box>
                </Fade>
            </Container>
        </FuturisticContainer>
    )
}

export default UpdateProduct;