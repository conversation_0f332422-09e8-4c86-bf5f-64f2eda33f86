import { Box, alpha } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

const float = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(30px) rotate(240deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
`;

const drift = keyframes`
  0% { transform: translateX(-100px); }
  100% { transform: translateX(calc(100vw + 100px)); }
`;

const TechBackgroundContainer = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  zIndex: -1,
  background: `linear-gradient(135deg, 
    ${alpha(theme.palette.primary.main, 0.02)} 0%, 
    ${alpha(theme.palette.secondary.main, 0.02)} 25%,
    ${alpha(theme.palette.accent?.main || theme.palette.primary.main, 0.02)} 50%,
    ${alpha(theme.palette.cyber?.main || theme.palette.secondary.main, 0.02)} 75%,
    ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, ${alpha(theme.palette.accent?.main || theme.palette.primary.main, 0.08)} 0%, transparent 50%)
    `,
  },
}));

const FloatingElement = styled(Box)(({ theme, delay = 0, size = 60, color = 'primary' }) => ({
  position: 'absolute',
  width: size,
  height: size,
  borderRadius: '50%',
  background: `linear-gradient(135deg, 
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.1)} 0%, 
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.05)} 100%)`,
  border: `1px solid ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.2)}`,
  animation: `${float} 6s ease-in-out infinite`,
  animationDelay: `${delay}s`,
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '60%',
    height: '60%',
    borderRadius: '50%',
    background: `radial-gradient(circle, ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.3)} 0%, transparent 70%)`,
    animation: `${pulse} 4s ease-in-out infinite`,
    animationDelay: `${delay * 0.5}s`,
  },
}));

const GridLine = styled(Box)(({ theme, direction = 'horizontal' }) => ({
  position: 'absolute',
  background: `linear-gradient(90deg, 
    transparent 0%, 
    ${alpha(theme.palette.primary.main, 0.1)} 50%, 
    transparent 100%)`,
  ...(direction === 'horizontal' ? {
    width: '100%',
    height: '1px',
    left: 0,
  } : {
    width: '1px',
    height: '100%',
    top: 0,
  }),
  animation: `${pulse} 8s ease-in-out infinite`,
}));

const DriftingParticle = styled(Box)(({ theme, delay = 0, duration = 20 }) => ({
  position: 'absolute',
  width: '4px',
  height: '4px',
  borderRadius: '50%',
  background: alpha(theme.palette.cyber?.main || theme.palette.secondary.main, 0.6),
  boxShadow: `0 0 10px ${alpha(theme.palette.cyber?.main || theme.palette.secondary.main, 0.8)}`,
  animation: `${drift} ${duration}s linear infinite`,
  animationDelay: `${delay}s`,
}));

const TechBackground = () => {
  return (
    <TechBackgroundContainer>
      {/* Grid Lines */}
      {Array.from({ length: 8 }).map((_, i) => (
        <GridLine
          key={`h-${i}`}
          direction="horizontal"
          sx={{ top: `${(i + 1) * 12.5}%` }}
        />
      ))}
      {Array.from({ length: 12 }).map((_, i) => (
        <GridLine
          key={`v-${i}`}
          direction="vertical"
          sx={{ left: `${(i + 1) * 8.33}%` }}
        />
      ))}

      {/* Floating Elements */}
      <FloatingElement
        sx={{ top: '10%', left: '10%' }}
        delay={0}
        size={80}
        color="primary"
      />
      <FloatingElement
        sx={{ top: '20%', right: '15%' }}
        delay={1}
        size={60}
        color="secondary"
      />
      <FloatingElement
        sx={{ bottom: '30%', left: '20%' }}
        delay={2}
        size={100}
        color="accent"
      />
      <FloatingElement
        sx={{ bottom: '15%', right: '25%' }}
        delay={3}
        size={70}
        color="cyber"
      />
      <FloatingElement
        sx={{ top: '50%', left: '50%' }}
        delay={1.5}
        size={40}
        color="primary"
      />

      {/* Drifting Particles */}
      {Array.from({ length: 15 }).map((_, i) => (
        <DriftingParticle
          key={i}
          sx={{ top: `${Math.random() * 100}%` }}
          delay={i * 2}
          duration={15 + Math.random() * 10}
        />
      ))}
    </TechBackgroundContainer>
  );
};

export default TechBackground;
