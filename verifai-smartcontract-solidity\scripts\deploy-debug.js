const hre = require("hardhat");

async function main() {
    console.log("🚀 Deploying Debug Verifai contract to Ganache...\n");

    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("VerifaiDebug");
        console.log("✅ Contract factory created");

        // Deploy the contract
        console.log("⏳ Deploying contract...");
        const verifaiContract = await VerifaiFactory.deploy();

        // Wait for deployment
        await verifaiContract.deployed();
        console.log("✅ Debug Verifai contract deployed successfully!");
        console.log("📍 Contract address:", verifaiContract.address);

        // Get deployer info
        const [deployer] = await hre.ethers.getSigners();
        console.log("👤 Deployed by:", deployer.address);

        // Test contract
        console.log("\n🔍 Testing debug contract...");
        try {
            const serialNumber = "debug123";
            
            console.log("📦 Registering test product...");
            const tx = await verifaiContract.registerProduct(
                "Debug Product",
                "Debug Brand", 
                serialNumber,
                "Debug Description",
                "debug.jpg",
                "Debug Manufacturer",
                "Debug Location",
                Math.floor(Date.now() / 1000).toString()
            );
            await tx.wait();
            console.log("✅ Test registration successful!");
            
            console.log("📦 Checking product exists...");
            const checkResult = await verifaiContract.checkProduct(serialNumber);
            console.log("✅ Check result:");
            console.log("   Exists:", checkResult.exists);
            console.log("   Stored Serial:", checkResult.storedSerial);
            console.log("   History Size:", checkResult.historySize.toString());
            
            if (checkResult.exists) {
                console.log("📦 Trying to get product...");
                const product = await verifaiContract.getProduct(serialNumber);
                console.log("✅ Product retrieval successful!");
                console.log("📦 Product name:", product[1]);
            } else {
                console.log("❌ Product doesn't exist after registration!");
            }
            
        } catch (error) {
            console.log("⚠️ Contract test failed:", error.message);
        }

        console.log("\n🎉 Debug Deployment Summary:");
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        console.log(`📍 Contract Address: ${verifaiContract.address}`);
        console.log(`🌐 Network: Ganache (Chain ID: 1337)`);
        console.log(`🔗 RPC URL: http://127.0.0.1:7545`);
        console.log(`👤 Deployer: ${deployer.address}`);
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        return verifaiContract.address;

    } catch (error) {
        console.error("❌ Deployment failed:", error.message);
        console.error("Full error:", error);
        process.exit(1);
    }
}

// Execute deployment
if (require.main === module) {
    main()
        .then((address) => {
            console.log(`\n✅ Debug deployment completed successfully!`);
            console.log(`Contract deployed at: ${address}`);
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Deployment script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
