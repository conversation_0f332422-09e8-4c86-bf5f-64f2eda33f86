{"project": {"name": "Verifa<PERSON>", "version": "1.0.0", "description": "Blockchain-Based Product Authentication System"}, "blockchain": {"contractAddress": "******************************************", "network": {"name": "Ganache Local", "rpcUrl": "http://127.0.0.1:7545", "chainId": 1337, "currency": {"name": "Ethereum", "symbol": "ETH", "decimals": 18}}, "ganacheAccounts": ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************"]}, "servers": {"frontend": {"url": "http://localhost:5173", "port": 5173}, "backend": {"url": "http://localhost:3000", "port": 3000}}, "deployment": {"lastUpdated": "2025-06-18T16:02:54.625Z", "deployedBy": "manual-update", "gasUsed": "unknown", "transactionHash": ""}, "files": {"frontend": ["verifai-frontend-react/src/utils/contractConfig.js", "verifai-frontend-react/src/components/pages/AddProduct.jsx", "verifai-frontend-react/src/components/pages/ScannerPage.jsx", "verifai-frontend-react/src/components/pages/Product.jsx", "verifai-frontend-react/src/components/pages/UpdateProductDetails.jsx", "verifai-frontend-react/src/components/pages/UpdateProduct.jsx", "verifai-frontend-react/public/test-metamask.html", "verifai-frontend-react/public/configure-metamask.html"], "smartContract": ["verifai-smartcontract-solidity/scripts/check-products.js", "verifai-smartcontract-solidity/scripts/check-specific-product.js", "verifai-smartcontract-solidity/scripts/simple-test.js", "verifai-smartcontract-solidity/ganache-deployment-fixed.json"], "documentation": ["COMPLETE_SOLUTION_GUIDE.md", "SUPPLY_CHAIN_SOLUTION.md"]}}