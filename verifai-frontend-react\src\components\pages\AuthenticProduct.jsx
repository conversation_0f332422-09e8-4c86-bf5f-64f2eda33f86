import { Box, Container, Typography, Fade, useTheme, alpha } from '@mui/material';
import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { styled, keyframes } from '@mui/material/styles';
import VerifiedIcon from '@mui/icons-material/Verified';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import SecurityIcon from '@mui/icons-material/Security';
import {
  FuturisticContainer,
  GlassCard,
  CyberButton,
  GradientText,
  NeonAvatar
} from '../common/FuturisticComponents';
import ParticleField from '../common/ParticleField';

// Professional animations
const successPulse = keyframes`
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
`;

const smoothSlideIn = keyframes`
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
`;

// Styled Components
const SuccessCard = styled(GlassCard)(({ theme }) => ({
  maxWidth: 600,
  width: '100%',
  padding: theme.spacing(6),
  textAlign: 'center',
  animation: `${smoothSlideIn} 0.8s cubic-bezier(0.4, 0, 0.2, 1)`,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: `linear-gradient(90deg,
      ${theme.palette.success.main} 0%,
      ${theme.palette.primary.main} 50%,
      ${theme.palette.success.dark} 100%
    )`,
    borderRadius: '24px 24px 0 0',
  },
}));

const SuccessIcon = styled(NeonAvatar)(({ theme }) => ({
  width: 120,
  height: 120,
  margin: '0 auto 24px',
  background: `linear-gradient(135deg,
    ${theme.palette.success.main} 0%,
    ${theme.palette.success.dark} 100%
  )`,
  animation: `${successPulse} 2s ease-in-out infinite`,
  boxShadow: `
    0 0 30px ${alpha(theme.palette.success.main, 0.4)},
    0 8px 32px ${alpha(theme.palette.common.black, 0.15)}
  `,
}));

const getEthereumObject = () => window.ethereum;

const findMetaMaskAccount = async () => {
    try {
        const ethereum = getEthereumObject();

        /*
         * First make sure we have access to the Ethereum object.
         */
        if (!ethereum) {
            console.error("Make sure you have Metamask!");
            return null;
        }

        console.log("We have the Ethereum object", ethereum);
        const accounts = await ethereum.request({ method: "eth_accounts" });

        if (accounts.length !== 0) {
            const account = accounts[0];
            console.log("Found an authorized account:", account);
            return account;
        } else {
            console.error("No authorized account found");
            return null;
        }
    } catch (error) {
        console.error(error);
        return null;
    }
};

const AuthenticProduct = () => {
    const [currentAccount, setCurrentAccount] = useState("");
    const navigate = useNavigate();
    const location = useLocation();
    const qrData = location.state?.qrData;
    const theme = useTheme();

    useEffect(() => {
        findMetaMaskAccount().then((account) => {
            if (account !== null) {
                setCurrentAccount(account);
            }
        });
    }, []);

    const connectWallet = async () => {
        try {
            const ethereum = getEthereumObject();
            if (!ethereum) {
                alert("Get MetaMask!");
                return;
            }

            const accounts = await ethereum.request({
                method: "eth_requestAccounts",
            });

            console.log("Connected", accounts[0]);
            setCurrentAccount(accounts[0]);
        } catch (error) {
            console.error(error);
        }
    };

    const handleClick = () => {
        connectWallet();
        
    }

    useEffect(() => {
        if(currentAccount){
            navigate('/product', { state: { qrData }});
        }
    }, [currentAccount]);

    console.log("qrdata: ", qrData);

    return (
        <FuturisticContainer>
            <ParticleField density="medium" animated={true} />

            <Container
                maxWidth="md"
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '100vh',
                    py: 4,
                    position: 'relative',
                    zIndex: 2
                }}
            >
                <SuccessCard>
                    {/* Success Icon */}
                    <Fade in timeout={600}>
                        <Box>
                            <SuccessIcon>
                                <VerifiedIcon sx={{ fontSize: 60 }} />
                            </SuccessIcon>
                        </Box>
                    </Fade>

                    {/* Success Message */}
                    <Fade in timeout={800}>
                        <Box sx={{ mb: 4 }}>
                            <GradientText variant="h3" component="h1" sx={{ mb: 2 }}>
                                Congratulations!
                            </GradientText>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontWeight: 600,
                                    color: 'success.main',
                                    mb: 3
                                }}
                            >
                                Your Product is Authentic
                            </Typography>
                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    fontSize: '1.1rem',
                                    lineHeight: 1.6,
                                    maxWidth: 400,
                                    mx: 'auto'
                                }}
                            >
                                Our verification system has confirmed this product's authenticity.
                                Connect your wallet to view detailed product information.
                            </Typography>
                        </Box>
                    </Fade>

                    {/* Wallet Connection Section */}
                    <Fade in timeout={1000}>
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            gap: 3
                        }}>
                            <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 2,
                                p: 2,
                                borderRadius: 2,
                                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
                            }}>
                                <SecurityIcon color="primary" />
                                <Typography variant="body2" color="text.secondary">
                                    Secure blockchain verification required
                                </Typography>
                            </Box>

                            <CyberButton
                                variant="primary"
                                onClick={handleClick}
                                startIcon={<AccountBalanceWalletIcon />}
                                sx={{
                                    px: 6,
                                    py: 2,
                                    fontSize: '1.1rem',
                                    minWidth: 280
                                }}
                            >
                                Connect Wallet
                            </CyberButton>
                        </Box>
                    </Fade>
                </SuccessCard>
            </Container>
        </FuturisticContainer>
    )


}

export default AuthenticProduct;