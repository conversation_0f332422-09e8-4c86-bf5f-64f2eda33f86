require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();
const { task } = require("hardhat/config");

// Define a task to print accounts (useful for debugging)
task("accounts", "Prints the list of accounts", async (taskArgs, hre) => {
  const accounts = await hre.ethers.getSigners();
  for (const account of accounts) {
    console.log(account.address);
  }
});

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: "0.8.17",
  networks: {
    // Local network configuration for Vite development
    hardhat: {
      chainId: 1337, // Standard chainId for local networks
      // Allows the frontend to connect to Hardhat's local blockchain
      forking: process.env.FORKING_URL ? {
        url: process.env.FORKING_URL
      } : undefined
    },
    // Localhost network (used when running 'npx hardhat node')
    localhost: {
      url: "http://127.0.0.1:8545",
      chainId: 1337,
    },
    // Ganache network configuration
    ganache: {
      url: "http://127.0.0.1:7545",
      chainId: 1337,
      gas: 6721975,
      gasPrice: ***********,
      accounts: [
        "0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d", // Account 0 - 100 ETH
        "0x6cbed15c793ce57650b9877cf6fa156fbef513c4e6134f022a85b1ffdd59b2a1", // Account 1 - 100 ETH
        "0x6370fd033278c143179d81c5526140625662b8daa446c22ee2d73db3707e620c", // Account 2 - 100 ETH
        "0x646f1ce2fdad0e6deeeb5c7e8e5543bdde65e86029e2fd9fc169899c440a7913", // Account 3 - 100 ETH
        "0xadd53f9a7e588d003326d1cbf9e4a43c061aadd9bc938c843a79e7b4fd2ad743"  // Account 4 - 100 ETH
      ]
    },
    // Test networks
    goerli: {
      url: process.env.GOERLI_API_KEY || "",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    sepolia: {
      url: process.env.SEPOLIA_QUICKNODE_KEY || "",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    // Mainnet
    mainnet: {
      url: process.env.PROD_QUICKNODE_KEY || "",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
  },
  // Path configuration for artifacts (important for Vite)
  paths: {
    artifacts: "./frontend/src/artifacts", // Points to your Vite project
    cache: "./cache",
    sources: "./contracts",
    tests: "./test",
  },
};
