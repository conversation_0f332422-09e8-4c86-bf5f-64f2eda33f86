import { Box, Button, styled, Typography } from "@mui/material";
import React from "react";

import buyIcon from "../../img/buy_icon.png";
import sellIcon from "../../img/sell_icon.png";
import rentIcon from "../../img/rent_icon.png";

import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import CustomButton from "./CustomButton";

const Guide = () => {
  const CustomBox = styled(Box)(({ theme }) => ({
    width: "30%",
    [theme.breakpoints.down("md")]: {
      width: "85%",
    },
  }));

  const GuidesBox = styled(Box)(({ theme }) => ({
    display: "flex",
    justifyContent: "space-around",
    width: "70%",
    marginTop: theme.spacing(5),
    marginBottom: theme.spacing(5),
    [theme.breakpoints.down("md")]: {
      width: "100%",
    },
    [theme.breakpoints.down("sm")]: {
      marginBottom: "0",
      flexDirection: "column",
    },
  }));

  const GuideBox = styled(Box)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginTop: theme.spacing(5),
    [theme.breakpoints.down("sm")]: {
      margin: theme.spacing(2, 0, 2, 0),
    },
  }));

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        marginBottom: "5rem",
      }}
    >
      <div
        style={{
          width: "5%",
          height: "5px",
          backgroundColor: "#000339",
          margin: "0 auto",
        }}
      ></div>

      <Typography
        variant="h3"
        sx={{ fontSize: "35px", fontWeight: "bold", color: "#000339", my: 3 }}
      >
        How it works?
      </Typography>

      <CustomBox>
        <Typography
          variant="body2"
          sx={{
            fontSize: "16px",
            fontWeight: "500",
            color: "#5A6473",
            textAlign: "center",
            marginBottom: "2rem",
          }}
        >
          Our groundbreaking counterfeit detection system for Global Rigorous Aura Success (M) Sdn Bhd harnesses the unparalleled power of blockchain technology to revolutionize product authentication. By assigning a tamper-proof digital ID to every item and recording it securely on the blockchain, we ensure unmatched transparency and security. Consumers of Global Rigorous Aura Success (M) Sdn Bhd can instantly verify the electronic product's authenticity by scanning its QR code on our platform, providing absolute confidence that the product is genuine and untouched by counterfeiting. This cutting-edge solution not only combats counterfeits but also safeguards loyal consumer's trust and safety, setting a new standard in anti-counterfeit technology.
        </Typography>
      </CustomBox>

      
    </Box>
  );
};

export default Guide;
