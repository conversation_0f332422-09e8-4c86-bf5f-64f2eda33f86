require('dotenv').config();
const rateLimit = require('express-rate-limit');
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { Client } = require('pg');
const path = require('path');
const multer = require('multer');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const cookieParser = require('cookie-parser');

const app = express();
app.use(bodyParser.json());
app.use(cookieParser());


app.use(cors({
    origin: function (origin, callback) {
        
        if (!origin) return callback(null, true);

        const allowedOrigins = [
            'http://localhost:5173',
            'http://localhost:3000',
            'http://127.0.0.1:5173',
            'http://127.0.0.1:3000',
            process.env.FRONTEND_URL
        ].filter(Boolean); 

        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            console.warn(`CORS blocked origin: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'Cookie',
        'X-Requested-With',
        'Accept',
        'Origin'
    ],
    exposedHeaders: ['Set-Cookie'],
    optionsSuccessStatus: 200 
}));

// Additional middleware to handle preflight requests and logging
app.use((req, res, next) => {
    // Log all requests for debugging
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url} - Origin: ${req.headers.origin || 'none'}`);

    if (req.method === 'OPTIONS') {
        res.header('Access-Control-Allow-Origin', req.headers.origin);
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept, Origin');
        res.header('Access-Control-Max-Age', '86400'); // 24 hours
        return res.status(200).end();
    }
    next();
});

const port = 3000;

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});
client.connect();

// Bcrypt salt rounds
const saltRounds = 10;

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m'; // Access token expires in 15 minutes
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d'; // Refresh token expires in 7 days

// JWT Middleware for protected routes
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({
            message: 'Access token required',
            code: 'TOKEN_REQUIRED'
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            if (err.name === 'TokenExpiredError') {
                return res.status(401).json({
                    message: 'Access token expired',
                    code: 'TOKEN_EXPIRED'
                });
            }
            return res.status(403).json({
                message: 'Invalid access token',
                code: 'TOKEN_INVALID'
            });
        }
        req.user = user;
        next();
    });
};

// Generate JWT tokens
const generateTokens = (user) => {
    const payload = {
        username: user.username,
        role: user.role,
        iat: Math.floor(Date.now() / 1000)
    };

    const accessToken = jwt.sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN,
        issuer: 'verifai-backend',
        audience: 'verifai-frontend'
    });

    const refreshToken = jwt.sign(payload, JWT_REFRESH_SECRET, {
        expiresIn: JWT_REFRESH_EXPIRES_IN,
        issuer: 'verifai-backend',
        audience: 'verifai-frontend'
    });

    return { accessToken, refreshToken };
};

// Rate limiter for login endpoint
const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 10,
    message: {
        status: 429,
        message: 
        'Too many login attempts from this IP, please try again after 15 minutes'
    }
});

// Only allow image file uploads
const imageFileFilter = (req, file, cb) => {
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedMimeTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Only JPEG, PNG, JPG, and WebP image files are allowed'), false);
    }
};

// Multer disk storage for profile images
const storageProfile = multer.diskStorage({
    destination: path.join(__dirname, 'public/uploads/profile'),
    filename: (req, file, cb) => {
        cb(null, file.originalname);
    }
});

// Multer disk storage for product images
const storageProduct = multer.diskStorage({
    destination: path.join(__dirname, 'public/uploads/product'),
    filename: (req, file, cb) => {
        cb(null, file.originalname);
    }
});


async function createAccount(username, password, role) {
    try {
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Try to insert with first_login column, fall back if column doesn't exist
        try {
            await client.query('INSERT INTO auth (username, password, role, first_login) VALUES ($1, $2, $3, $4)',
                [username, hashedPassword, role, true]);
        } catch (error) {
            // If first_login column doesn't exist, use basic insert
            console.log('first_login column not found, using basic insert');
            await client.query('INSERT INTO auth (username, password, role) VALUES ($1, $2, $3)',
                [username, hashedPassword, role]);
        }

        console.log('Account created successfully');
    } catch (err) {
        console.error('Error creating account:', err.message);
    }
}


async function changePassword(username, newPassword) {
    try {
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Try to update with first_login column, fall back if column doesn't exist
        try {
            await client.query('UPDATE auth SET password = $1, first_login = $2 WHERE username = $3',
                [hashedPassword, false, username]);
        } catch (error) {
            // If first_login column doesn't exist, use basic update
            console.log('first_login column not found, using basic password update');
            await client.query('UPDATE auth SET password = $1 WHERE username = $2',
                [hashedPassword, username]);
        }

        console.log('Password updated successfully');
    } catch (err) {
        console.error('Error updating password:', err.message);
    }
}

async function verifyPassword(username, password) {
    try {
        const result = await client.query('SELECT password FROM auth WHERE username = $1', [username]);
        if (result.rows.length > 0) {
            const hashedPasswordFromDb = result.rows[0].password;
            return await bcrypt.compare(password, hashedPasswordFromDb);
        }
        return false;
    } catch (err) {
        console.error('Error verifying password:', err.message);
        return false;
    }
}


function createProfile(username, name, description, website, location, image, role) {
    client.query(
        'INSERT INTO profile (username, name, description, website, location, image, role) VALUES ($1, $2, $3, $4, $5, $6, $7)',
        [username, name, description, website, location, image, role],
        (err) => {
            if (err) console.log(err.message);
            else console.log('Profile data inserted successfully');
        }
    );
}

function addProduct(serialNumber, name, brand) {
    client.query(
        'INSERT INTO product (serialNumber, name, brand) VALUES ($1, $2, $3)',
        [serialNumber, name, brand],
        (err) => {
            if (err) console.log(err.message);
            else console.log('Product data inserted successfully');
        }
    );
}


app.get('/authAll', async (req, res) => {
    try {
        const data = await client.query('SELECT * FROM auth');
        res.header('Access-Control-Allow-Credentials', true);
        res.send(data.rows);
    } catch (err) {
        console.error("Error fetching auth data:", err.message);
        res.status(500).send("Error fetching data");
    }
});

app.post('/auth', loginLimiter, async (req, res) => {
    const { username, password } = req.body;

    try {
        const isAuthenticated = await verifyPassword(username, password);

        if (isAuthenticated) {
            // Get user data - try to include first_login flag if column exists
            let data;
            try {
                data = await client.query('SELECT username, role, first_login FROM auth WHERE username = $1', [username]);
            } catch (error) {
                // If first_login column doesn't exist, fall back to basic query
                console.log('first_login column not found, using basic auth query');
                data = await client.query('SELECT username, role FROM auth WHERE username = $1', [username]);
            }

            const user = data.rows[0];

            // Check if this is a first-time login (only if first_login column exists)
            if (user.first_login === true) {
                // Return special response for first-time login
                res.json({
                    message: 'First time login - password change required',
                    requirePasswordChange: true,
                    username: user.username,
                    code: 'FIRST_LOGIN'
                });
                return;
            }

            // Generate JWT tokens for regular login
            const { accessToken, refreshToken } = generateTokens(user);

            // Set refresh token as httpOnly cookie
            res.cookie('refreshToken', refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
            });

            // Send access token and user info
            res.json({
                message: 'Authentication successful',
                user: { username: user.username, role: user.role },
                accessToken: accessToken,
                tokenType: 'Bearer',
                expiresIn: JWT_EXPIRES_IN
            });
        } else {
            res.status(401).json({
                message: 'Authentication failed',
                code: 'INVALID_CREDENTIALS'
            });
        }
    } catch (error) {
        console.error('Authentication error:', error);
        res.status(500).json({
            message: 'Internal server error',
            code: 'SERVER_ERROR'
        });
    }
});

app.post('/addaccount', async (req, res) => {
    const { username, password, role } = req.body;
    await createAccount(username, password, role);
    res.send('Account created successfully');
});

// Verify current password endpoint (for password change flow)
app.post('/verify-current-password', async (req, res) => {
    const { username, currentPassword } = req.body;

    try {
        const isAuthenticated = await verifyPassword(username, currentPassword);

        if (isAuthenticated) {
            res.json({
                valid: true,
                message: 'Current password verified'
            });
        } else {
            res.status(401).json({
                valid: false,
                message: 'Current password is incorrect'
            });
        }
    } catch (error) {
        console.error('Password verification error:', error);
        res.status(500).json({
            valid: false,
            message: 'Internal server error'
        });
    }
});

app.post('/changepsw', async (req, res) => {
    const { username, currentPassword, password } = req.body;

    try {
        // First verify the current password
        if (currentPassword) {
            const isCurrentPasswordValid = await verifyPassword(username, currentPassword);
            if (!isCurrentPasswordValid) {
                return res.status(401).json({
                    message: 'Current password is incorrect',
                    code: 'INVALID_CURRENT_PASSWORD'
                });
            }
        }

        // If current password is valid (or not provided for backward compatibility), change password
        await changePassword(username, password);
        res.json({
            message: 'Password updated successfully',
            success: true
        });
    } catch (error) {
        console.error('Password change error:', error);
        res.status(500).json({
            message: 'Error updating password',
            code: 'PASSWORD_CHANGE_FAILED'
        });
    }
});

// Token verification endpoint
app.get('/auth/verify', authenticateToken, async (req, res) => {
    try {
        // If we reach here, the token is valid (middleware passed)
        const user = req.user;

        res.json({
            valid: true,
            user: {
                username: user.username,
                role: user.role
            }
        });
    } catch (error) {
        console.error('Token verification error:', error);
        res.status(401).json({
            valid: false,
            message: 'Token verification failed',
            code: 'VERIFICATION_FAILED'
        });
    }
});

// Token refresh endpoint
app.post('/auth/refresh', async (req, res) => {
    const { refreshToken } = req.cookies;

    if (!refreshToken) {
        return res.status(401).json({
            message: 'Refresh token required',
            code: 'REFRESH_TOKEN_REQUIRED'
        });
    }

    try {
        const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET);

        // Verify user still exists
        const data = await client.query('SELECT username, role FROM auth WHERE username = $1', [decoded.username]);

        if (data.rows.length === 0) {
            return res.status(401).json({
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        const user = data.rows[0];
        const { accessToken, refreshToken: newRefreshToken } = generateTokens(user);

        // Set new refresh token as httpOnly cookie
        res.cookie('refreshToken', newRefreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
        });

        res.json({
            accessToken: accessToken,
            tokenType: 'Bearer',
            expiresIn: JWT_EXPIRES_IN
        });

    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                message: 'Refresh token expired',
                code: 'REFRESH_TOKEN_EXPIRED'
            });
        }
        return res.status(403).json({
            message: 'Invalid refresh token',
            code: 'REFRESH_TOKEN_INVALID'
        });
    }
});


app.post('/auth/logout', (req, res) => {
   
    res.clearCookie('refreshToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
    });

    res.json({
        message: 'Logout successful',
        code: 'LOGOUT_SUCCESS'
    });
});

// Verify token endpoint
app.get('/auth/verify', authenticateToken, (req, res) => {
    res.json({
        valid: true,
        user: {
            username: req.user.username,
            role: req.user.role
        }
    });
});


app.get('/profileAll', async (req, res) => {
    try {
        const data = await client.query('SELECT * FROM profile');
        res.header('Access-Control-Allow-Credentials', true);
        res.send(data.rows);
    } catch (err) {
        console.error("Error fetching profile data:", err.message);
        res.status(500).send("Error fetching data");
    }
});

app.get('/profile/:username', authenticateToken, async (req, res) => {
    const { username } = req.params;

    // Users can only access their own profile unless they're admin
    if (req.user.username !== username && req.user.role !== 'admin') {
        return res.status(403).json({
            message: 'Access denied. You can only access your own profile.',
            code: 'ACCESS_DENIED'
        });
    }

    try {
        const data = await client.query('SELECT * FROM profile WHERE username = $1', [username]);
        res.json(data.rows);
    } catch (err) {
        console.error("Error fetching profile:", err.message);
        res.status(500).json({
            message: "Error fetching profile data",
            code: 'SERVER_ERROR'
        });
    }
});

app.post('/addprofile', (req, res) => {
    const { username, name, description, website, location, image, role } = req.body;
    createProfile(username, name, description, website, location, image, role);
    res.send('Profile data inserted successfully');
});


app.post('/upload/profile', (req, res) => {
    const upload = multer({ storage: storageProfile, fileFilter: imageFileFilter }).single('image');
    upload(req, res, (err) => {
        if (!req.file) return res.status(400).send('Please select a valid image file');
        if (err instanceof multer.MulterError) return res.status(500).send('Multer error: ' + err.message);
        if (err) return res.status(400).send(err.message);
        res.send('Profile image uploaded successfully');
    });
});

app.post('/upload/product', (req, res) => {
    const upload = multer({ storage: storageProduct, fileFilter: imageFileFilter }).single('image');
    upload(req, res, (err) => {
        if (!req.file) return res.status(400).send('Please select a valid image file');
        if (err instanceof multer.MulterError) return res.status(500).send('Multer error: ' + err.message);
        if (err) return res.status(400).send(err.message);
        res.send('Product image uploaded successfully');
    });
});


app.get('/file/profile/:fileName', (req, res) => {
    const filePath = path.join(__dirname, 'public/uploads/profile', req.params.fileName);
    res.sendFile(filePath);
});

app.get('/file/product/:fileName', (req, res) => {
    const filePath = path.join(__dirname, 'public/uploads/product', req.params.fileName);
    res.sendFile(filePath);
});


app.get('/product/serialNumber', async (req, res) => {
    try {
        const data = await client.query('SELECT serialNumber FROM product');
        res.send(data.rows);
    } catch (err) {
        console.error("Error fetching serial numbers:", err.message);
        res.status(500).send("Error fetching data");
    }
});

app.post('/addproduct', (req, res) => {
    const { serialNumber, name, brand } = req.body;
    addProduct(serialNumber, name, brand);
    res.send('Product data inserted successfully');
});


app.get('/favicon.ico', (req, res) => {
    res.status(204).end(); 
});


app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});


app.get('/', (req, res) => {
    res.json({
        message: 'VERIFAI Backend API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});


app.use('*', (req, res) => {
    console.log(` 404 - Route not found: ${req.method} ${req.originalUrl}`);
    res.status(404).json({
        message: 'Route not found',
        method: req.method,
        url: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});

app.listen(port, () => {
    console.log(` Server is running on port ${port}`);
});