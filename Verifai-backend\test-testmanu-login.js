const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testTestManuLogin() {
    console.log('🧪 Testing testmanu account login flow\n');

    try {
        // Test 1: Login with testmanu account (should require password change)
        console.log('1️⃣ Testing login with testmanu (first-time user)...');
        const loginResponse = await axios.post(`${BASE_URL}/auth`, {
            username: 'testmanu',
            password: 'testpass123'
        });

        if (loginResponse.data.requirePasswordChange) {
            console.log('✅ Login correctly detected first-time user');
            console.log(`   Message: ${loginResponse.data.message}`);
            console.log(`   Username returned: ${loginResponse.data.username}`);
        } else {
            console.log('❌ Login should have required password change');
            console.log('   Response:', loginResponse.data);
            return;
        }

        // Test 2: Try to change password with wrong current password
        console.log('\n2️⃣ Testing password change with wrong current password...');
        try {
            await axios.post(`${BASE_URL}/changepsw`, {
                username: 'testmanu',
                currentPassword: 'wrongpassword',
                password: 'NewSecure123!'
            });
            console.log('❌ Should have failed with wrong current password');
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('✅ Correctly rejected wrong current password');
                console.log(`   Error: ${error.response.data.message}`);
            } else {
                console.log('❌ Unexpected error:', error.message);
            }
        }

        // Test 3: Change password with correct current password
        console.log('\n3️⃣ Testing password change with correct current password...');
        try {
            const changeResponse = await axios.post(`${BASE_URL}/changepsw`, {
                username: 'testmanu',
                currentPassword: 'testpass123',
                password: 'NewSecure123!'
            });
            
            if (changeResponse.data.success) {
                console.log('✅ Password changed successfully');
                console.log(`   Message: ${changeResponse.data.message}`);
            } else {
                console.log('❌ Password change failed');
            }
        } catch (error) {
            console.log('❌ Password change error:', error.response?.data?.message || error.message);
            return;
        }

        // Test 4: Try to login with old password (should fail)
        console.log('\n4️⃣ Testing login with old password...');
        try {
            await axios.post(`${BASE_URL}/auth`, {
                username: 'testmanu',
                password: 'testpass123'
            });
            console.log('❌ Should not be able to login with old password');
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('✅ Correctly rejected old password');
            } else {
                console.log('❌ Unexpected error:', error.message);
            }
        }

        // Test 5: Login with new password (should work normally)
        console.log('\n5️⃣ Testing login with new password...');
        try {
            const newLoginResponse = await axios.post(`${BASE_URL}/auth`, {
                username: 'testmanu',
                password: 'NewSecure123!'
            });

            if (newLoginResponse.data.accessToken && !newLoginResponse.data.requirePasswordChange) {
                console.log('✅ Successfully logged in with new password');
                console.log('✅ No password change required (first_login = false)');
                console.log(`   User: ${newLoginResponse.data.user.username} (${newLoginResponse.data.user.role})`);
            } else if (newLoginResponse.data.requirePasswordChange) {
                console.log('❌ Should not require password change after first change');
            } else {
                console.log('❌ Login failed with new password');
            }
        } catch (error) {
            console.log('❌ Login with new password failed:', error.response?.data?.message || error.message);
        }

        console.log('\n🎉 testmanu password change flow test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('   Response:', error.response.data);
        }
    }
}

// Run the test
testTestManuLogin();
