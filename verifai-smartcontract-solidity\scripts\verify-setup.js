const hre = require("hardhat");

async function main() {
    console.log("🔍 Verifying Smart Contract Setup...\n");

    // Get the contract factory
    const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
    
    // Contract address from environment or hardcoded
    const contractAddress = process.env.CONTRACT_ADDRESS || "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully");
        
        // Test contract owner
        try {
            const owner = await contract.owner();
            console.log("👤 Contract Owner:", owner);
        } catch (error) {
            console.log("❌ Could not get contract owner:", error.message);
        }
        
        // Test with the serial number from QR code
        const testSerialNumber = "popoos";
        console.log(`\n🔍 Testing product lookup for serial: "${testSerialNumber}"`);
        
        try {
            const product = await contract.getProduct(testSerialNumber);
            console.log("📦 Product data:", product);
            
            if (product && product[0] && product[0] !== "") {
                console.log("✅ Product found!");
                console.log("   Serial Number:", product[0]);
                console.log("   Name:", product[1]);
                console.log("   Brand:", product[2]);
                console.log("   Description:", product[3]);
                console.log("   Image:", product[4]);
                console.log("   History Length:", product[5]?.length || 0);
                
                if (product[5] && product[5].length > 0) {
                    console.log("📋 Product History:");
                    product[5].forEach((entry, index) => {
                        console.log(`   ${index + 1}. Actor: ${entry.actor}, Location: ${entry.location}, Timestamp: ${entry.timestamp}, Sold: ${entry.isSold}`);
                    });
                } else {
                    console.log("⚠️  No history entries found");
                }
            } else {
                console.log("❌ Product not found or empty data returned");
                console.log("   This means the product was not registered on this contract");
            }
        } catch (error) {
            console.log("❌ Error getting product:", error.message);
            if (error.message.includes("could not decode result data")) {
                console.log("   This usually means the product doesn't exist on the blockchain");
            }
        }
        
        // Test contract functions availability
        console.log("\n🔧 Testing contract functions...");
        try {
            // Test if we can call registerProduct (this will fail but tells us if function exists)
            const registerFunction = contract.interface.getFunction("registerProduct");
            console.log("✅ registerProduct function available");
            
            const getProductFunction = contract.interface.getFunction("getProduct");
            console.log("✅ getProduct function available");
            
            const addHistoryFunction = contract.interface.getFunction("addProductHistory");
            console.log("✅ addProductHistory function available");
            
        } catch (error) {
            console.log("❌ Error checking contract functions:", error.message);
        }
        
    } catch (error) {
        console.log("❌ Failed to connect to contract:", error.message);
        console.log("   Check if the contract address is correct and the contract is deployed");
    }
    
    console.log("\n🏁 Verification complete!");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Verification failed:", error);
        process.exit(1);
    });
