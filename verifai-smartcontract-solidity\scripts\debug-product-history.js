const hre = require("hardhat");

async function main() {
    console.log("🔍 Debugging product history in blockchain...\n");

    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully\n");
        
        // Check the specific product from your screenshot
        const serialNumber = "dffg";
        
        console.log(`🔍 Debugging product with serial number: "${serialNumber}"`);
        
        try {
            const product = await contract.getProduct(serialNumber);
            
            console.log("✅ Product found!");
            console.log("📦 Raw Product Data:");
            console.log("   Serial Number:", product[0]);
            console.log("   Name:", product[1]);
            console.log("   Brand:", product[2]);
            console.log("   Description:", product[3]);
            console.log("   Image:", product[4]);
            console.log("   History Array:", product[5]);
            console.log("   History Array Length:", product[5].length);
            console.log("   History Array Type:", typeof product[5]);
            
            console.log("\n📋 Detailed History Analysis:");
            for (let i = 0; i < product[5].length; i++) {
                const historyEntry = product[5][i];
                console.log(`\n   History Entry ${i + 1}:`);
                console.log(`     ID: ${historyEntry.id}`);
                console.log(`     Actor: ${historyEntry.actor}`);
                console.log(`     Location: ${historyEntry.location}`);
                console.log(`     Timestamp: ${historyEntry.timestamp}`);
                console.log(`     Date: ${new Date(historyEntry.timestamp * 1000).toLocaleString()}`);
                console.log(`     Is Sold: ${historyEntry.isSold}`);
                console.log(`     Is Sold Type: ${typeof historyEntry.isSold}`);
            }
            
            console.log("\n🎯 Analysis:");
            if (product[5].length === 1) {
                console.log("⚠️  Only 1 history entry found - this means no supply chain updates have been added");
                console.log("💡 Expected: Multiple entries for manufacturer → supplier → retailer → sale");
            } else if (product[5].length === 2) {
                console.log("⚠️  Only 2 history entries found - missing some supply chain updates");
            } else {
                console.log(`✅ Found ${product[5].length} history entries - good supply chain tracking`);
            }
            
            console.log("\n🔧 To add supply chain updates:");
            console.log("1. Suppliers should scan and update when they receive the product");
            console.log("2. Retailers should scan and update when they receive the product (isSold=false)");
            console.log("3. Retailers should scan and update when they sell the product (isSold=true)");
            
        } catch (error) {
            console.log(`❌ Product not found for serial number: "${serialNumber}"`);
            console.log("Error:", error.message);
            
            console.log("\n💡 This means the product hasn't been registered yet");
            console.log("🔧 Register the product first, then test supply chain updates");
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
    }
}

// Execute the debug
if (require.main === module) {
    main()
        .then(() => {
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
