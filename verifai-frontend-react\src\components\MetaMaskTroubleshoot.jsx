import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Alert,
  Chip,
  Stack
} from '@mui/material';
import {
  ExpandMore,
  CheckCircle,
  Error,
  Refresh,
  Download,
  Settings,
  Security
} from '@mui/icons-material';

const MetaMaskTroubleshoot = () => {
  const troubleshootingSteps = [
    {
      title: "MetaMask Not Installed",
      problem: "Extension not found error",
      solutions: [
        "Download MetaMask from the official website: https://metamask.io/",
        "Install the browser extension for Chrome, Firefox, or Edge",
        "Restart your browser after installation",
        "Refresh the page and try again"
      ]
    },
    {
      title: "MetaMask Not Connecting",
      problem: "Wallet connection fails",
      solutions: [
        "Click the MetaMask extension icon in your browser",
        "Make sure you're logged into MetaMask",
        "Click 'Connect' when prompted by the website",
        "Check if the correct account is selected",
        "Try refreshing the page and connecting again"
      ]
    },
    {
      title: "Wrong Network",
      problem: "Connected to wrong blockchain network",
      solutions: [
        "Open MetaMask and check the network dropdown",
        "Switch to the correct network (Ethereum Mainnet or required testnet)",
        "If the network isn't available, add it manually",
        "Contact support for network configuration details"
      ]
    },
    {
      title: "Transaction Failures",
      problem: "Blockchain transactions not working",
      solutions: [
        "Ensure you have enough ETH for gas fees",
        "Check if MetaMask is unlocked",
        "Try increasing the gas limit",
        "Wait for network congestion to clear",
        "Reset MetaMask transaction history if needed"
      ]
    },
    {
      title: "Browser Issues",
      problem: "Extension conflicts or browser problems",
      solutions: [
        "Disable other wallet extensions temporarily",
        "Clear browser cache and cookies",
        "Try using an incognito/private browsing window",
        "Update your browser to the latest version",
        "Try a different browser (Chrome recommended)"
      ]
    }
  ];

  const quickChecks = [
    { label: "MetaMask Extension Installed", icon: <Download /> },
    { label: "Browser Updated", icon: <Refresh /> },
    { label: "MetaMask Unlocked", icon: <Security /> },
    { label: "Correct Network Selected", icon: <Settings /> },
    { label: "Sufficient ETH Balance", icon: <CheckCircle /> }
  ];

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom color="primary" fontWeight={600}>
          MetaMask Troubleshooting Guide
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          Having trouble with MetaMask? Follow this guide to resolve common issues.
        </Typography>

        {/* Quick Status Checks */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Quick Status Check
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {quickChecks.map((check, index) => (
              <Chip
                key={index}
                icon={check.icon}
                label={check.label}
                variant="outlined"
                size="small"
              />
            ))}
          </Stack>
        </Alert>

        {/* Troubleshooting Steps */}
        <Typography variant="h6" gutterBottom sx={{ mt: 4, mb: 2 }}>
          Common Issues & Solutions
        </Typography>

        {troubleshootingSteps.map((step, index) => (
          <Accordion key={index} sx={{ mb: 1 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Error color="warning" />
                <Box>
                  <Typography variant="subtitle1" fontWeight={600}>
                    {step.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.problem}
                  </Typography>
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {step.solutions.map((solution, solutionIndex) => (
                  <ListItem key={solutionIndex}>
                    <ListItemIcon>
                      <CheckCircle color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={solution} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        ))}

        {/* Emergency Actions */}
        <Alert severity="warning" sx={{ mt: 4 }}>
          <Typography variant="subtitle2" gutterBottom>
            Still Having Issues?
          </Typography>
          <Typography variant="body2" paragraph>
            If none of the above solutions work, try these emergency steps:
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <Button
              variant="outlined"
              size="small"
              href="https://metamask.io/download/"
              target="_blank"
              startIcon={<Download />}
            >
              Reinstall MetaMask
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={() => window.location.reload()}
              startIcon={<Refresh />}
            >
              Refresh Page
            </Button>
            <Button
              variant="outlined"
              size="small"
              href="https://support.metamask.io/"
              target="_blank"
            >
              MetaMask Support
            </Button>
          </Stack>
        </Alert>

        {/* Browser Compatibility */}
        <Box sx={{ mt: 4, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>
            Browser Compatibility
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>Recommended:</strong> Chrome, Firefox, Brave, Edge<br />
            <strong>Not Supported:</strong> Safari (limited support), Internet Explorer<br />
            <strong>Mobile:</strong> Use MetaMask mobile app with built-in browser
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default MetaMaskTroubleshoot;
