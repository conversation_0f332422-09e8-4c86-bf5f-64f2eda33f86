import React, { useRef } from 'react';
import { Box, alpha, useTheme } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

// Professional subtle animations
const gentleFloat = keyframes`
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
  50% { transform: translateY(-20px) scale(1.05); opacity: 0.8; }
`;

const subtleDrift = keyframes`
  0% { transform: translateX(0px); }
  50% { transform: translateX(30px); }
  100% { transform: translateX(0px); }
`;

const softPulse = keyframes`
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
`;

const elegantRotate = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const ParticleContainer = styled(Box)({
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  pointerEvents: 'none',
  zIndex: 0,
  overflow: 'hidden',
});

// Professional floating particles
const FloatingParticle = styled(Box)(({ theme, delay = 0, duration = 12, size = 4 }) => ({
  position: 'absolute',
  width: size,
  height: size,
  background: `radial-gradient(circle,
    ${alpha(theme.palette.primary.main, 0.7)} 0%,
    ${alpha(theme.palette.primary.main, 0.3)} 50%,
    transparent 100%
  )`,
  borderRadius: '50%',
  animation: `${gentleFloat} ${duration}s cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  animationDelay: `${delay}s`,
  filter: 'blur(0.5px)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'scale(1.5)',
    filter: 'blur(0px)',
  },
}));

// Subtle energy lines
const EnergyLine = styled(Box)(({ theme, delay = 0, duration = 15, color = 'primary' }) => ({
  position: 'absolute',
  width: '2px',
  height: '60px',
  background: `linear-gradient(180deg,
    transparent 0%,
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.5)} 30%,
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.7)} 50%,
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.5)} 70%,
    transparent 100%
  )`,
  borderRadius: '1px',
  animation: `${subtleDrift} ${duration}s cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  animationDelay: `${delay}s`,
  opacity: 0.6,
  filter: 'blur(0.5px)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    opacity: 1,
    filter: 'blur(0px)',
  },
}));

// Professional glowing orbs
const GlowingOrb = styled(Box)(({ theme, delay = 0, size = 8, color = 'primary' }) => ({
  position: 'absolute',
  width: size,
  height: size,
  borderRadius: '50%',
  background: `radial-gradient(circle,
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.8)} 0%,
    ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.3)} 50%,
    transparent 100%
  )`,
  animation: `${softPulse} 8s ease-in-out infinite`,
  animationDelay: `${delay}s`,
  filter: 'blur(2px)',
}));

// Elegant geometric shapes
const GeometricShape = styled(Box)(({ theme, delay = 0, size = 12, color = 'primary' }) => ({
  position: 'absolute',
  width: size,
  height: size,
  background: 'transparent',
  border: `1px solid ${alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.3)}`,
  borderRadius: '2px',
  animation: `${elegantRotate} 20s linear infinite, ${gentleFloat} 12s ease-in-out infinite`,
  animationDelay: `${delay}s`,
  opacity: 0.4,
}));

const ProfessionalParticles = ({
  density = 'medium',
  effects = ['floating', 'energy', 'glowing', 'geometric'],
  animated = true
}) => {
  const theme = useTheme();
  const containerRef = useRef(null);

  const particleCount = {
    low: { floating: 6, energy: 3, glowing: 4, geometric: 2 },
    medium: { floating: 10, energy: 5, glowing: 6, geometric: 4 },
    high: { floating: 15, energy: 8, glowing: 10, geometric: 6 },
  }[density];

  const generateParticles = (type, count) => {
    const particles = [];
    const colors = ['primary', 'secondary'];

    for (let i = 0; i < count; i++) {
      particles.push({
        id: `${type}-${i}`,
        type,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 6 + 3,
        top: Math.random() * 100,
        left: Math.random() * 100,
        delay: Math.random() * 8,
        duration: Math.random() * 8 + 10,
      });
    }

    return particles;
  };

  const allParticles = effects.reduce((acc, effect) => {
    if (particleCount[effect]) {
      acc.push(...generateParticles(effect, particleCount[effect]));
    }
    return acc;
  }, []);

  const renderParticle = (particle) => {
    const { id, ...otherProps } = particle;
    const commonProps = {
      delay: otherProps.delay,
      duration: otherProps.duration,
      color: otherProps.color,
      size: otherProps.size,
      sx: {
        top: `${otherProps.top}%`,
        left: `${otherProps.left}%`,
      },
    };

    switch (particle.type) {
      case 'floating':
        return <FloatingParticle key={id} {...commonProps} />;
      case 'energy':
        return <EnergyLine key={id} {...commonProps} />;
      case 'glowing':
        return <GlowingOrb key={id} {...commonProps} />;
      case 'geometric':
        return <GeometricShape key={id} {...commonProps} />;
      default:
        return null;
    }
  };

  if (!animated) return null;

  return (
    <ParticleContainer ref={containerRef}>
      {allParticles.map(renderParticle)}
    </ParticleContainer>
  );
};

export default ProfessionalParticles;
