import { Box, Paper, Avatar, Typography, Button } from '@mui/material';
import bgImg from '../../img/bg.png';
import { useState, useEffect } from 'react';
import { apiGet } from '../../utils/apiUtils';
import useAuth from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

const Profile = () => {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [role, setRole] = useState('');
    const [website, setWebsite] = useState('');
    const [location, setLocation] = useState('');
    const [image, setImage] = useState({
        file: [],
        filepreview: null
    });

    const { auth, user, isAuthenticated, isLoading } = useAuth();
    const navigate = useNavigate();

    const handleBack = () => {
        navigate(-1);
    };

    const handleData = async () => {
        try {
            // Get the username from the auth object
            const username = auth?.user?.username || user?.username;

            if (!username) {
                console.warn('No username available for profile lookup');
                return;
            }

            console.log('Fetching profile for username:', username);

            const res = await apiGet(`/profile/${username}`);

            if (res.data && Array.isArray(res.data) && res.data.length > 0) {
                const profile = res.data[0];

                console.log("Fetched profile data:", profile);

                setName(profile.name || '');
                setDescription(profile.description || '');
                setRole(profile.role || '');
                setWebsite(profile.website || '');
                setLocation(profile.location || '');
                setImage({
                    file: [],
                    filepreview: profile.image ? `${process.env.REACT_APP_API_URL || 'http://localhost:3000'}/file/profile/${profile.image}` : null
                });
            } else {
                console.warn("Profile data is empty or not an array.");
            }
        } catch (error) {
            console.error("Error fetching profile data:", error);

            if (error.response?.status === 403) {
                console.error('Access forbidden - check authentication and permissions');
            } else if (error.response?.status === 401) {
                console.error('Unauthorized - token may be expired or invalid');
            }
        }
    };

    useEffect(() => {
        // Wait for authentication to be loaded
        if (isLoading) {
            console.log('Authentication still loading...');
            return;
        }

        if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            navigate('/login');
            return;
        }

        handleData();
    }, [isLoading, isAuthenticated, navigate, auth, user]);

    return (
        <Box
            sx={{
                backgroundImage: `url(${bgImg})`,
                minHeight: "100vh",
                backgroundRepeat: "no-repeat",
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                backgroundSize: 'cover',
                zIndex: -2,
                overflowY: "scroll"
            }}
        >
            <Paper
                elevation={3}
                sx={{
                    width: "400px",
                    margin: "auto",
                    marginTop: "10%",
                    marginBottom: "10%",
                    padding: "3%",
                    backgroundColor: "#e3eefc"
                }}
            >
                <Avatar
                    sx={{
                        width: 100,
                        height: 100,
                        margin: "auto",
                        marginBottom: "3%",
                        backgroundColor: "#3f51b5"
                    }}
                >
                    {name ? name[0] : "?"}
                </Avatar>

                <Typography variant="h4" sx={{ textAlign: "center", marginBottom: "5%" }}>
                    {name || 'No Name'}
                </Typography>

                <Typography variant="body1" sx={{ textAlign: "center", marginBottom: "3%" }}>
                    Description: {description || 'N/A'}
                </Typography>

                <Typography variant="body1" sx={{ textAlign: "center", marginBottom: "3%" }}>
                    Role: {role || 'N/A'}
                </Typography>

                <Typography variant="body1" sx={{ textAlign: "center", marginBottom: "3%" }}>
                    Website: {website || 'N/A'}
                </Typography>

                <Typography variant="body1" sx={{ textAlign: "center", marginBottom: "3%" }}>
                    Location: {location || 'N/A'}
                </Typography>

                <Box sx={{ width: "100%", display: "flex", justifyContent: "center" }}>
                    <Button onClick={handleBack} sx={{ marginTop: "7%" }}>
                        Back
                    </Button>
                </Box>
            </Paper>
        </Box>
    );
};

export default Profile;
