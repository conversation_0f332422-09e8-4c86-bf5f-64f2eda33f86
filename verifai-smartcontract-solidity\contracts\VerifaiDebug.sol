// SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.17; 

import "hardhat/console.sol";

contract VerifaiDebug{
    address public owner; 

    struct Product {
        string name;
        string serialNumber;
        string description;
        string brand;
        string image;
        mapping(uint => ProductHistory) history;
        uint historySize;
    }

    mapping(string => Product) products;
    mapping(uint => ProductHistory) history;    

    struct ProductHistory {
        uint id;
        string actor;
        string location;
        string timestamp;
        bool isSold;
    }

    function registerProduct(string memory _name, string memory _brand, string memory _serialNumber, string memory _description, string memory _image,
    string memory _actor, string memory _location, string memory _timestamp) public {
        console.log("=== REGISTER PRODUCT DEBUG ===");
        console.log("Serial Number: %s", _serialNumber);
        console.log("Name: %s", _name);
        
        // Check if product already exists
        console.log("Checking if product exists...");
        console.log("Current serial number in storage: %s", products[_serialNumber].serialNumber);
        console.log("Length of current serial: %s", bytes(products[_serialNumber].serialNumber).length);
        
        require(bytes(products[_serialNumber].serialNumber).length == 0, "Product with this serial number already exists");

        Product storage p = products[_serialNumber];

        console.log("Setting product data...");
        p.name = _name;
        p.brand = _brand;
        p.serialNumber = _serialNumber;
        p.description = _description;
        p.image = _image;
        p.historySize = 0;

        console.log("Product data set. Serial: %s", p.serialNumber);
        console.log("Product name: %s", p.name);

        addProductHistory(_serialNumber,_actor, _location, _timestamp, false);
        
        console.log("=== REGISTER COMPLETE ===");
        console.log("Final serial in storage: %s", products[_serialNumber].serialNumber);
        console.log("Final history size: %s", products[_serialNumber].historySize);
    }

    function addProductHistory(string memory _serialNumber, string memory _actor, 
    string memory _location, string memory _timestamp, bool _isSold) public {
        console.log("=== ADD HISTORY DEBUG ===");
        console.log("Serial Number: %s", _serialNumber);
        console.log("Actor: %s", _actor);
        
        Product storage p = products[_serialNumber];
        console.log("Current history size: %s", p.historySize);
        
        p.historySize++;
        p.history[p.historySize] = ProductHistory(p.historySize, _actor, _location, _timestamp, _isSold);

        console.log("New history size: %s", p.historySize);
        console.log("Product History added: %s", p.history[p.historySize].actor);
        console.log("Product name: %s", p.name);
        console.log("=== ADD HISTORY COMPLETE ===");
    }

    function getProduct(string memory _serialNumber) public view returns (string memory, string memory, 
    string memory, string memory, string memory, ProductHistory[] memory) {
        console.log("=== GET PRODUCT DEBUG ===");
        console.log("Requested serial: %s", _serialNumber);
        console.log("Stored serial: %s", products[_serialNumber].serialNumber);
        console.log("Stored serial length: %s", bytes(products[_serialNumber].serialNumber).length);
        console.log("History size: %s", products[_serialNumber].historySize);
        
        // Check if product exists
        require(bytes(products[_serialNumber].serialNumber).length > 0, "Product with this serial number does not exist");
        
        ProductHistory[] memory pHistory = new ProductHistory[](products[_serialNumber].historySize);

        for (uint i = 0; i < products[_serialNumber].historySize ; i++) {
            pHistory[i] = products[_serialNumber].history[i+1];            
        }

        console.log("=== GET PRODUCT COMPLETE ===");
        return (products[_serialNumber].serialNumber, products[_serialNumber].name, products[_serialNumber].brand,products[_serialNumber].description, products[_serialNumber].image, pHistory);
    }

    // Debug function to check if product exists
    function checkProduct(string memory _serialNumber) public view returns (bool exists, string memory storedSerial, uint historySize) {
        return (
            bytes(products[_serialNumber].serialNumber).length > 0,
            products[_serialNumber].serialNumber,
            products[_serialNumber].historySize
        );
    }
}
