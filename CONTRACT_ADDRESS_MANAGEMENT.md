# 🔐 Contract Address Management System

## Overview

This system provides centralized management of smart contract addresses across the entire Verifai project. Instead of manually updating contract addresses in multiple files, you can now update them in one place and have the changes automatically propagate to all relevant files.

## 📁 Files in This System

### Core Files
- **`project-config.json`** - Master configuration file (single source of truth)
- **`set-contract-address.js`** - Simple utility for updating contract addresses
- **`update-contract-address.js`** - Automation script that updates all project files

### Automatically Updated Files
- All React components (`AddProduct.jsx`, `ScannerPage.jsx`, etc.)
- Smart contract testing scripts
- HTML configuration pages
- Documentation files
- JSON configuration files

## 🚀 Quick Usage

### 1. Update Contract Address (Recommended Method)
```bash
# Set new contract address and update all files automatically
node set-contract-address.js ******************************************
```

### 2. Show Current Configuration
```bash
# Display current contract address and network settings
node set-contract-address.js --show
```

### 3. Update All Files (Without Changing Address)
```bash
# Propagate current address to all files
node set-contract-address.js --update
```

### 4. Manual Configuration Update
```bash
# Edit project-config.json manually, then run:
node update-contract-address.js
```

## 📋 Detailed Commands

### Set New Contract Address
```bash
node set-contract-address.js <new-contract-address>
```
**What it does:**
- Validates the contract address format
- Updates `project-config.json`
- Automatically runs the update script
- Updates all project files
- Provides success confirmation

**Example:**
```bash
node set-contract-address.js ******************************************
```

### Show Current Configuration
```bash
node set-contract-address.js --show
# or
node set-contract-address.js -s
```
**Output:**
```
📋 Current Configuration:

Contract Address: ******************************************
Network: Ganache Local
Chain ID: 1337
RPC URL: http://127.0.0.1:7545
Last Updated: 2025-01-18T12:00:00.000Z
Deployed By: deploy-ganache.js
```

### Update All Files
```bash
node set-contract-address.js --update
# or
node set-contract-address.js -u
```
**What it does:**
- Reads current address from `project-config.json`
- Updates all React components
- Updates smart contract scripts
- Updates HTML files
- Updates documentation

## 🔄 Automatic Integration

### During Contract Deployment
The deployment script (`deploy-ganache.js`) automatically:
1. Deploys the smart contract
2. Updates `project-config.json` with the new address
3. Runs the update script to propagate changes
4. Updates all project files

### Manual Integration
You can integrate this system into your own scripts:
```javascript
const { updateContractAddress, runUpdateScript } = require('./set-contract-address.js');

// Update contract address
if (updateContractAddress(newAddress, {
    deployedBy: 'my-script',
    gasUsed: '2500000',
    transactionHash: '0x...'
})) {
    // Run automatic updates
    runUpdateScript();
}
```

## 📂 Project Configuration Structure

The `project-config.json` file contains:

```json
{
  "blockchain": {
    "contractAddress": "******************************************",
    "network": {
      "name": "Ganache Local",
      "rpcUrl": "http://127.0.0.1:7545",
      "chainId": 1337
    }
  },
  "deployment": {
    "lastUpdated": "2025-01-18T12:00:00.000Z",
    "deployedBy": "deploy-ganache.js",
    "gasUsed": "Estimated 3M"
  },
  "files": {
    "frontend": [...],
    "smartContract": [...],
    "documentation": [...]
  }
}
```

## ✅ Benefits

### Before (Manual Updates)
- ❌ Update 15+ files manually
- ❌ Risk of missing files
- ❌ Inconsistent addresses
- ❌ Time-consuming process
- ❌ Error-prone

### After (Centralized System)
- ✅ Update one command
- ✅ All files updated automatically
- ✅ Consistent addresses guaranteed
- ✅ Fast and efficient
- ✅ Error-free process

## 🛠️ Troubleshooting

### Invalid Contract Address
```bash
❌ Invalid contract address format: 0x123
   Expected format: 0x followed by 40 hexadecimal characters
```
**Solution:** Ensure your address is exactly 42 characters (0x + 40 hex digits)

### File Not Found Errors
```bash
⚠️ File not found: verifai-frontend-react/src/components/pages/SomeComponent.jsx
```
**Solution:** This is normal if files have been moved or renamed. The script will skip missing files.

### Permission Errors
```bash
❌ Failed to update file: Permission denied
```
**Solution:** Ensure you have write permissions to the project files.

## 🔧 Advanced Usage

### Custom Network Configuration
Edit `project-config.json` to change network settings:
```json
{
  "blockchain": {
    "network": {
      "name": "Custom Network",
      "rpcUrl": "http://localhost:8545",
      "chainId": 1337
    }
  }
}
```

### Adding New Files to Auto-Update
Add file paths to the `files` section in `project-config.json`:
```json
{
  "files": {
    "frontend": [
      "path/to/new/component.jsx"
    ]
  }
}
```

## 🎯 Best Practices

1. **Always use the centralized system** instead of manual file editing
2. **Run the update script** after any manual config changes
3. **Commit the project-config.json** file to version control
4. **Test your application** after address updates
5. **Keep backups** of working configurations

---

**🎉 Enjoy hassle-free contract address management!**
