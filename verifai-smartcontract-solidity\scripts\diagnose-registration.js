const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function diagnoseRegistration() {
    try {
        console.log("🔍 Diagnosing blockchain registration issues...\n");

        // Get network info
        const network = await ethers.provider.getNetwork();
        console.log("📡 Network Information:");
        console.log(`  Chain ID: ${network.chainId}`);
        console.log(`  Network Name: ${network.name}`);

        // Check if we're on local hardhat network
        const isLocalNetwork = network.chainId === 31337n || network.chainId === 1337n;
        console.log(`  Local Network: ${isLocalNetwork ? 'Yes' : 'No'}`);

        // Get latest block
        const latestBlock = await ethers.provider.getBlockNumber();
        console.log(`  Latest Block: ${latestBlock}`);

        // Check block time
        const block = await ethers.provider.getBlock(latestBlock);
        console.log(`  Block Timestamp: ${new Date(Number(block.timestamp) * 1000).toLocaleString()}`);

        // Get accounts
        const accounts = await ethers.getSigners();
        console.log(`\n👥 Available Accounts: ${accounts.length}`);
        
        for (let i = 0; i < Math.min(3, accounts.length); i++) {
            const balance = await ethers.provider.getBalance(accounts[i].address);
            console.log(`  Account ${i}: ${accounts[i].address} (${ethers.utils.formatEther(balance)} ETH)`);
        }

        // Check contract deployment
        const contractAddress = "******************************************";
        console.log(`\n📋 Contract Information:`);
        console.log(`  Address: ${contractAddress}`);

        // Check if contract exists
        const code = await ethers.provider.getCode(contractAddress);
        const contractExists = code !== "0x";
        console.log(`  Contract Deployed: ${contractExists ? 'Yes' : 'No'}`);

        if (contractExists) {
            console.log(`  Contract Code Size: ${code.length} bytes`);

            // Load contract ABI
            const artifactPath = path.join(__dirname, '../artifacts/contracts/Verifai.sol/Verifai.json');
            if (fs.existsSync(artifactPath)) {
                const artifact = JSON.parse(fs.readFileSync(artifactPath, 'utf8'));
                const contract = new ethers.Contract(contractAddress, artifact.abi, accounts[0]);

                console.log(`  Contract ABI Loaded: Yes`);

                // Test basic contract functions
                try {
                    console.log("\n🧪 Testing Contract Functions:");
                    
                    // Test if we can call a view function
                    console.log("  Testing view functions...");
                    
                    // Try to get a product (this should not revert even if product doesn't exist)
                    try {
                        const testResult = await contract.getProduct("TEST123");
                        console.log("  ✅ getProduct function works");
                    } catch (error) {
                        console.log(`  ⚠️ getProduct function issue: ${error.message}`);
                    }

                    // Check gas price
                    const gasPrice = await ethers.provider.getFeeData();
                    console.log(`\n⛽ Gas Information:`);
                    console.log(`  Gas Price: ${gasPrice.gasPrice ? ethers.utils.formatUnits(gasPrice.gasPrice, 'gwei') + ' gwei' : 'Not available'}`);
                    console.log(`  Max Fee Per Gas: ${gasPrice.maxFeePerGas ? ethers.utils.formatUnits(gasPrice.maxFeePerGas, 'gwei') + ' gwei' : 'Not available'}`);

                    // Estimate gas for a sample registration
                    try {
                        console.log("\n🔍 Testing Gas Estimation:");
                        const gasEstimate = await contract.registerProduct.estimateGas(
                            "Test Product",
                            "Test Brand", 
                            "TEST123",
                            "Test Description",
                            "test.jpg",
                            "Test Manufacturer",
                            "0.0000, 0.0000",
                            Math.floor(Date.now() / 1000).toString()
                        );
                        console.log(`  Estimated Gas: ${gasEstimate.toString()}`);
                        console.log(`  ✅ Gas estimation successful`);
                    } catch (gasError) {
                        console.log(`  ❌ Gas estimation failed: ${gasError.message}`);
                        
                        // Check if it's a revert reason
                        if (gasError.message.includes("revert")) {
                            console.log("  This might indicate a contract logic issue");
                        }
                    }

                } catch (contractError) {
                    console.log(`  ❌ Contract interaction failed: ${contractError.message}`);
                }
            } else {
                console.log(`  ❌ Contract ABI not found at: ${artifactPath}`);
            }
        }

        // Check pending transactions
        console.log("\n⏳ Checking Network Status:");
        try {
            const pendingBlock = await ethers.provider.getBlock("pending");
            if (pendingBlock && pendingBlock.transactions.length > 0) {
                console.log(`  Pending Transactions: ${pendingBlock.transactions.length}`);
            } else {
                console.log(`  Pending Transactions: 0`);
            }
        } catch (error) {
            console.log(`  Could not check pending transactions: ${error.message}`);
        }

        // Performance recommendations
        console.log("\n💡 Performance Analysis:");
        
        if (isLocalNetwork) {
            console.log("  ✅ Using local network - should be fast");
            console.log("  💡 If slow, check if Hardhat node is running properly");
        } else {
            console.log("  ⚠️ Using external network - may be slower");
            console.log("  💡 Consider using local network for development");
        }

        if (latestBlock < 10) {
            console.log("  ⚠️ Very few blocks - network might be new or slow");
        }

        console.log("\n🔧 Troubleshooting Steps:");
        console.log("  1. Ensure Hardhat node is running: npx hardhat node");
        console.log("  2. Check MetaMask is connected to correct network");
        console.log("  3. Verify contract address in frontend matches deployed address");
        console.log("  4. Check browser console for detailed error messages");
        console.log("  5. Try with a smaller gas limit if estimation fails");

    } catch (error) {
        console.error("❌ Diagnosis failed:", error.message);
        console.error("Full error:", error);
    }
}

// Run diagnosis
diagnoseRegistration()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
