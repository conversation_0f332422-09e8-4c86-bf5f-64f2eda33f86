import React, { useState } from 'react';
import {
  AppBar,
  <PERSON>lbar,
  Typography,
  Button,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Box,
  Chip,
  Divider,
  useTheme,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  alpha,
} from '@mui/material';
import {
  Menu as MenuIcon,
  AccountCircle,
  Logout,
  Dashboard,
  QrCodeScanner,
  Security,
  Person,
  Business,
  Store,
  Factory,
  Home,
  Login as LoginIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { styled, keyframes } from '@mui/material/styles';
import useAuth from '../../hooks/useAuth';
import { NeonAvatar, StatusChip } from '../common/FuturisticComponents';
import { NavigationIcon, useIconColors } from '../common/EnhancedIcon';

// Enhanced animations
const glow = keyframes`
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.6); }
`;

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.95)} 0%,
    ${alpha(theme.palette.background.paper, 0.9)} 100%
  )`,
  backdropFilter: 'blur(25px)',
  borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
  color: theme.palette.text.primary,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '2px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  },
}));

const Logo = styled(Typography)(({ theme }) => ({
  fontWeight: 800,
  fontSize: '1.5rem',
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  cursor: 'pointer',
}));

const NavButton = styled(Button)(({ theme }) => ({
  textTransform: 'none',
  borderRadius: '8px',
  padding: '8px 16px',
  fontWeight: 500,
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: theme.palette.primary.main + '10',
    color: theme.palette.primary.main,
  },
  '&.active': {
    color: theme.palette.primary.main,
    fontWeight: 600,
  },
}));

const UserAvatar = styled(NeonAvatar)(({ theme }) => ({
  width: 45,
  height: 45,
  cursor: 'pointer',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  animation: `${glow} 4s ease-in-out infinite`,
  '&:hover': {
    transform: 'scale(1.1)',
    animation: `${glow} 1s ease-in-out infinite`,
  },
}));

const AppNavbar = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const { auth, logout, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, navigate to login
      navigate('/login', { replace: true });
    } finally {
      handleMenuClose();
    }
  };

  const getNavItems = () => {
    const publicItems = [
      { label: 'Home', path: '/', icon: <Home /> },
      { label: 'Scanner', path: '/scanner', icon: <QrCodeScanner /> },
    ];

    if (!isAuthenticated) {
      return [
        ...publicItems,
        { label: 'Login', path: '/login', icon: <LoginIcon /> },
      ];
    }

    const roleBasedItems = {
      admin: [
        { label: 'Dashboard', path: '/admin', icon: <Security /> },
        { label: 'Add Account', path: '/add-account', icon: <Person /> },
        { label: 'Manage Accounts', path: '/manage-account', icon: <Dashboard /> },
      ],
      manufacturer: [
        { label: 'Dashboard', path: '/manufacturer', icon: <Factory /> },
        { label: 'Add Product', path: '/add-product', icon: <Business /> },
        { label: 'Profile', path: '/profile', icon: <Person /> },
      ],
      supplier: [
        { label: 'Dashboard', path: '/supplier', icon: <Business /> },
        { label: 'Update Product', path: '/scanner', icon: <QrCodeScanner /> },
        { label: 'Profile', path: '/profile', icon: <Person /> },
      ],
      retailer: [
        { label: 'Dashboard', path: '/retailer', icon: <Store /> },
        { label: 'Update Product', path: '/scanner', icon: <QrCodeScanner /> },
        { label: 'Profile', path: '/profile', icon: <Person /> },
      ],
    };

    return [
      ...publicItems,
      ...(roleBasedItems[user?.role] || []),
    ];
  };

  const navItems = getNavItems();

  const renderDesktopNav = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {navItems.slice(0, 4).map((item, index) => (
        <NavButton
          key={`${item.path}-${item.label}-${index}`}
          onClick={() => navigate(item.path)}
          className={location.pathname === item.path ? 'active' : ''}
        >
          {item.label}
        </NavButton>
      ))}
    </Box>
  );

  const renderMobileDrawer = () => (
    <Drawer
      variant="temporary"
      open={mobileOpen}
      onClose={handleDrawerToggle}
      ModalProps={{ keepMounted: true }}
      sx={{
        '& .MuiDrawer-paper': {
          boxSizing: 'border-box',
          width: 280,
          backgroundColor: 'background.paper',
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Logo onClick={() => navigate('/')}>Verifai</Logo>
      </Box>
      <Divider />
      <List>
        {navItems.map((item, index) => (
          <ListItem key={`${item.path}-${item.label}-${index}`} disablePadding>
            <ListItemButton
              onClick={() => {
                navigate(item.path);
                handleDrawerToggle();
              }}
              selected={location.pathname === item.path}
              sx={{
                borderRadius: 1,
                mx: 1,
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main + '15',
                  color: theme.palette.primary.main,
                },
              }}
            >
              <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Drawer>
  );

  const renderUserMenu = () => (
    <Menu
      id="user-menu"
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={handleMenuClose}
      MenuListProps={{
        'aria-labelledby': 'user-menu-button',
        role: 'menu',
      }}
      PaperProps={{
        sx: {
          mt: 1.5,
          minWidth: 200,
          borderRadius: 2,
          boxShadow: theme.shadows[3],
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      disableAutoFocusItem={false}
      disableRestoreFocus={false}
    >
      <Box sx={{ p: 3, pb: 2 }}>
        <Typography variant="subtitle1" fontWeight={700} sx={{ mb: 1 }}>
          {auth?.user?.username || user?.username || 'User'}
        </Typography>
        <StatusChip
          label={auth?.user?.role || user?.role || 'Guest'}
          status="success"
          size="small"
        />
      </Box>
      <Divider />
      <MenuItem
        onClick={() => { navigate('/profile'); handleMenuClose(); }}
        role="menuitem"
        aria-label="Go to profile page"
      >
        <ListItemIcon>
          <NavigationIcon size="small">
            <AccountCircle fontSize="small" />
          </NavigationIcon>
        </ListItemIcon>
        Profile
      </MenuItem>
      <MenuItem
        onClick={handleLogout}
        role="menuitem"
        aria-label="Logout from account"
      >
        <ListItemIcon>
          <NavigationIcon size="small" variant="error">
            <Logout fontSize="small" />
          </NavigationIcon>
        </ListItemIcon>
        Logout
      </MenuItem>
    </Menu>
  );

  return (
    <>
      <StyledAppBar position="sticky" elevation={0}>
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Logo onClick={() => navigate('/')}>Verifai</Logo>
          </Box>

          {!isMobile && renderDesktopNav()}

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {isAuthenticated ? (
              <>
                <Button
                  variant="outlined"
                  onClick={handleLogout}
                  startIcon={<Logout />}
                  sx={{
                    borderRadius: 2,
                    borderWidth: 2,
                    fontWeight: 600,
                    '&:hover': {
                      borderWidth: 2,
                      transform: 'translateY(-1px)',
                    },
                  }}
                >
                  Logout
                </Button>
                <UserAvatar
                  onClick={handleProfileMenuOpen}
                  id="user-menu-button"
                  aria-controls={Boolean(anchorEl) ? 'user-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
                  aria-label="User account menu"
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleProfileMenuOpen(e);
                    }
                  }}
                >
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </UserAvatar>
              </>
            ) : (
              <Button
                variant="contained"
                onClick={() => navigate('/login')}
                sx={{
                  borderRadius: 2,
                  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
                  },
                }}
              >
                Login
              </Button>
            )}
          </Box>
        </Toolbar>
      </StyledAppBar>

      {isMobile && renderMobileDrawer()}
      {isAuthenticated && renderUserMenu()}
    </>
  );
};

export default AppNavbar;
