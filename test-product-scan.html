<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Scan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .qr-code {
            background: #000;
            color: white;
            padding: 20px;
            text-align: center;
            font-family: monospace;
            font-size: 18px;
            border-radius: 5px;
            margin: 20px 0;
            word-break: break-all;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            background: #ffe7e7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #d00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Product Scan</h1>
        
        <div class="info">
            <h3>📋 Instructions:</h3>
            <p>1. Make sure the blockchain is running (Hardhat node)</p>
            <p>2. Make sure the frontend is running (npm run vite)</p>
            <p>3. Click the test buttons below to simulate QR code scanning</p>
        </div>

        <h2>Test QR Codes</h2>
        
        <h3>Test Product 1: "popoos"</h3>
        <div class="qr-code">
            0x2fcc261bB32262a150E4905F6d550D4FF05bC582,popoos
        </div>
        <a href="javascript:void(0)"
           onclick="testProductDirect('0x2fcc261bB32262a150E4905F6d550D4FF05bC582,popoos')"
           class="test-button">
            🔍 Test Product "popoos"
        </a>

        <h3>Test Product 2: "TEST123"</h3>
        <div class="qr-code">
            0x2fcc261bB32262a150E4905F6d550D4FF05bC582,TEST123
        </div>
        <a href="javascript:void(0)"
           onclick="testProductDirect('0x2fcc261bB32262a150E4905F6d550D4FF05bC582,TEST123')"
           class="test-button">
            🔍 Test Product "TEST123"
        </a>

        <h3>Test Product 3: "WTEST123"</h3>
        <div class="qr-code">
            0x2fcc261bB32262a150E4905F6d550D4FF05bC582,WTEST123
        </div>
        <a href="javascript:void(0)"
           onclick="testProductDirect('0x2fcc261bB32262a150E4905F6d550D4FF05bC582,WTEST123')"
           class="test-button">
            🔍 Test Product "WTEST123"
        </a>

        <h3>Direct URL Test</h3>
        <a href="http://localhost:5173/product?qr=0x2fcc261bB32262a150E4905F6d550D4FF05bC582%2Cpopoos"
           target="_blank"
           class="test-button">
            🔗 Direct URL Test
        </a>

        <div class="info">
            <h3>🔧 Debugging:</h3>
            <p><strong>Contract Address:</strong> 0x2fcc261bB32262a150E4905F6d550D4FF05bC582</p>
            <p><strong>Frontend URL:</strong> http://localhost:5173</p>
            <p><strong>Blockchain URL:</strong> http://localhost:8545</p>
        </div>

        <div class="error">
            <h3>⚠️ If products don't load:</h3>
            <p>1. Check browser console for errors</p>
            <p>2. Make sure MetaMask is connected to localhost:8545</p>
            <p>3. Make sure the contract address matches</p>
            <p>4. Check if products exist in the blockchain</p>
        </div>
    </div>

    <script>
        function testProduct(qrData) {
            // Store the QR data in localStorage for the product page to read
            localStorage.setItem('testQrData', qrData);
            console.log('🔍 Testing product with QR data:', qrData);

            // Navigate to product page with state
            const url = new URL('http://localhost:5173/product');
            window.open(url.href, '_blank');

            // Also try to pass via URL params as backup
            setTimeout(() => {
                const urlWithParams = new URL('http://localhost:5173/product');
                urlWithParams.searchParams.set('qr', encodeURIComponent(qrData));
                console.log('🔗 Alternative URL:', urlWithParams.href);
            }, 1000);
        }

        function testProductDirect(qrData) {
            console.log('🔍 Testing product directly with QR data:', qrData);

            // Navigate directly with QR data in URL
            const url = new URL('http://localhost:5173/product');
            url.searchParams.set('qr', encodeURIComponent(qrData));
            console.log('🔗 Opening URL:', url.href);
            window.open(url.href, '_blank');
        }

        // Display current environment info
        console.log('🌐 Test Environment:');
        console.log('   Frontend: http://localhost:5173');
        console.log('   Blockchain: http://localhost:8545');
        console.log('   Contract: 0x2fcc261bB32262a150E4905F6d550D4FF05bC582');
    </script>
</body>
</html>
