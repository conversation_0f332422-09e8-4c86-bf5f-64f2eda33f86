import { Box, alpha } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { useTheme } from '@mui/material/styles';

const smoothFloat = keyframes`
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
    filter: blur(0px);
  }
  25% {
    transform: translateY(-15px) rotate(45deg) scale(1.05);
    opacity: 0.9;
    filter: blur(0.5px);
  }
  50% {
    transform: translateY(-8px) rotate(90deg) scale(1.1);
    opacity: 1;
    filter: blur(1px);
  }
  75% {
    transform: translateY(12px) rotate(135deg) scale(1.05);
    opacity: 0.8;
    filter: blur(0.5px);
  }
`;

const breathingPulse = keyframes`
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.4;
    filter: brightness(1);
  }
  33% {
    transform: scale(1.3) rotate(120deg);
    opacity: 0.7;
    filter: brightness(1.2);
  }
  66% {
    transform: scale(1.6) rotate(240deg);
    opacity: 0.9;
    filter: brightness(1.4);
  }
`;

const fluidDrift = keyframes`
  0% {
    transform: translateX(-40px) translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  20% {
    transform: translateX(20px) translateY(-25px) rotate(72deg);
    opacity: 0.8;
  }
  40% {
    transform: translateX(80px) translateY(-10px) rotate(144deg);
    opacity: 1;
  }
  60% {
    transform: translateX(60px) translateY(20px) rotate(216deg);
    opacity: 0.9;
  }
  80% {
    transform: translateX(10px) translateY(15px) rotate(288deg);
    opacity: 0.7;
  }
  100% {
    transform: translateX(-40px) translateY(0px) rotate(360deg);
    opacity: 0.5;
  }
`;

const gentleWave = keyframes`
  0%, 100% {
    transform: translateY(0px) scaleY(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-5px) scaleY(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scaleY(1.2);
    opacity: 1;
  }
  75% {
    transform: translateY(-5px) scaleY(1.1);
    opacity: 0.8;
  }
`;

const ParticleContainer = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  pointerEvents: 'none',
  zIndex: 0,
});

const Particle = styled(Box)(({ theme, size = 4, delay = 0, duration = 10, type = 'circle' }) => ({
  position: 'absolute',
  width: size,
  height: size,
  borderRadius: type === 'circle' ? '50%' : type === 'square' ? '0%' : '20%',
  background: type === 'glow'
    ? `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.8)} 0%, ${alpha(theme.palette.primary.main, 0.4)} 50%, transparent 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.7)} 0%, ${alpha(theme.palette.secondary.main, 0.7)} 100%)`,
  boxShadow: type === 'glow'
    ? `0 0 ${size * 3}px ${alpha(theme.palette.primary.main, 0.6)}, 0 0 ${size * 6}px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 0 ${size * 2}px ${alpha(theme.palette.primary.main, 0.4)}, 0 0 ${size * 4}px ${alpha(theme.palette.primary.main, 0.2)}`,
  animation: type === 'float'
    ? `${smoothFloat} ${duration}s cubic-bezier(0.4, 0, 0.2, 1) infinite`
    : type === 'pulse'
    ? `${breathingPulse} ${duration}s cubic-bezier(0.4, 0, 0.2, 1) infinite`
    : `${fluidDrift} ${duration}s cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  animationDelay: `${delay}s`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'scale(1.2)',
    filter: 'brightness(1.3)',
  },
}));

const ConnectingLine = styled(Box)(({ theme, delay = 0 }) => ({
  position: 'absolute',
  width: '2px',
  height: '100px',
  background: `linear-gradient(180deg,
    transparent 0%,
    ${alpha(theme.palette.primary.main, 0.4)} 30%,
    ${alpha(theme.palette.secondary.main, 0.5)} 50%,
    ${alpha(theme.palette.primary.main, 0.4)} 70%,
    transparent 100%)`,
  transformOrigin: 'center',
  animation: `${gentleWave} 12s cubic-bezier(0.4, 0, 0.2, 1) infinite`,
  animationDelay: `${delay}s`,
  filter: 'blur(0.5px)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    filter: 'blur(0px)',
    background: `linear-gradient(180deg,
      transparent 0%,
      ${alpha(theme.palette.primary.main, 0.6)} 30%,
      ${alpha(theme.palette.secondary.main, 0.7)} 50%,
      ${alpha(theme.palette.primary.main, 0.6)} 70%,
      transparent 100%)`,
  },
}));

const ParticleField = ({ density = 'medium', animated = true }) => {
  const theme = useTheme();
  
  const particleCount = {
    low: 15,
    medium: 25,
    high: 40,
  }[density];

  const generateParticles = () => {
    const particles = [];
    
    for (let i = 0; i < particleCount; i++) {
      const types = ['circle', 'square', 'rounded', 'glow'];
      const animations = ['float', 'pulse', 'drift'];
      
      particles.push({
        id: i,
        type: types[Math.floor(Math.random() * types.length)],
        animation: animations[Math.floor(Math.random() * animations.length)],
        size: Math.random() * 8 + 2,
        top: Math.random() * 100,
        left: Math.random() * 100,
        delay: Math.random() * 10,
        duration: Math.random() * 15 + 10,
      });
    }
    
    return particles;
  };

  const generateLines = () => {
    const lines = [];
    const lineCount = Math.floor(particleCount / 3);
    
    for (let i = 0; i < lineCount; i++) {
      lines.push({
        id: i,
        top: Math.random() * 100,
        left: Math.random() * 100,
        delay: Math.random() * 5,
        rotation: Math.random() * 360,
      });
    }
    
    return lines;
  };

  const particles = generateParticles();
  const lines = generateLines();

  if (!animated) return null;

  return (
    <ParticleContainer>
      {/* Connecting Lines */}
      {lines.map((line) => (
        <ConnectingLine
          key={`line-${line.id}`}
          delay={line.delay}
          sx={{
            top: `${line.top}%`,
            left: `${line.left}%`,
            transform: `rotate(${line.rotation}deg)`,
          }}
        />
      ))}
      
      {/* Particles */}
      {particles.map((particle) => (
        <Particle
          key={particle.id}
          size={particle.size}
          delay={particle.delay}
          duration={particle.duration}
          type={particle.animation}
          sx={{
            top: `${particle.top}%`,
            left: `${particle.left}%`,
          }}
        />
      ))}
      
      {/* Special glow particles */}
      {Array.from({ length: 5 }).map((_, i) => (
        <Particle
          key={`glow-${i}`}
          size={20 + Math.random() * 30}
          delay={i * 2}
          duration={20 + Math.random() * 10}
          type="glow"
          sx={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
        />
      ))}
    </ParticleContainer>
  );
};

export default ParticleField;
