# 🔄 **VERIFAI RESTART SOLUTION**

## 🎯 **Problem Solved**

**Issue**: Every time you restart terminal/Ganache, you face contract deployment errors and need to manually redeploy everything.

**Solution**: One-command automatic restart that fixes everything!

## 🎯 **ONE-COMMAND CONTRACT ADDRESS UPDATES**

### **🚀 NEW: Centralized Contract Address Management**
Instead of manually editing 15+ files, use the new centralized system:

```bash
# Set new contract address (updates ALL files automatically)
node set-contract-address.js 0x2948dcd1B5537E3C0a596716b908AE23ab06CDa9

# Show current configuration
node set-contract-address.js --show

# Update all files with current address
node set-contract-address.js --update
```

## 🚀 **COMPLETE PROJECT RESTART PROCESS**

### **Option 1: From Root Directory**
```bash
# 🎯 SINGLE COMMAND TO FIX EVERYTHING
npm run restart-project

# Alternative commands (all do the same thing)
npm run fix-contract
npm run deploy-fresh
node restart-verifai.js
```

### **Option 2: From Smart Contract Directory**
```bash
cd verifai-smartcontract-solidity
npm run deploy-and-setup

# Alternative commands
npm run restart-fix
npm run quick-deploy
```

## ✅ **What This Does Automatically**

1. **🔧 Compiles** smart contracts
2. **🚀 Deploys** VerifaiDebug contract to Ganache
3. **📦 Registers** test products:
   - `test123` - Test Product
   - `dffg` - Your QR code product
4. **🔄 Updates** all frontend files with new contract address using centralized system
5. **📋 Updates** project-config.json with deployment information
6. **🔄 Propagates** contract address to all 15+ files automatically
7. **✅ Verifies** everything is working
8. **🎉 Ready to use** - no manual steps!

## 🎯 **Centralized Address Management Benefits**

- ✅ **No more manual editing** of 15+ files
- ✅ **One command updates everything** automatically
- ✅ **Consistent addresses** across all components
- ✅ **Configuration tracking** in project-config.json
- ✅ **Error prevention** with address validation
- ✅ **Time saving** - seconds instead of minutes

## 🎯 **Usage After Restart**

**Every time you restart terminal/Ganache:**

1. **Start Ganache** (make sure it's running on port 7545)
2. **Run the fix command**:
   ```bash
   npm run restart-project
   ```
3. **If you need to update contract address manually**:
   ```bash
   # Check current address
   node set-contract-address.js --show

   # Set new address (if needed)
   node set-contract-address.js 0x2948dcd1B5537E3C0a596716b908AE23ab06CDa9
   ```
4. **Start your frontend**:
   ```bash
   cd verifai-frontend-react
   npm run vite
   ```
5. **✅ Everything works!**

## 📋 **Ready-to-Use QR Codes**

After running the restart command, these QR codes will work immediately:

- **Format**: `ContractAddress,SerialNumber`
- **Current Contract**: `0x2948dcd1B5537E3C0a596716b908AE23ab06CDa9`
- **Example**: `0x2948dcd1B5537E3C0a596716b908AE23ab06CDa9,test123`
- **Example**: `0x2948dcd1B5537E3C0a596716b908AE23ab06CDa9,dffg`

**💡 Pro Tip**: Use `node set-contract-address.js --show` to always get the current contract address!

## 🔧 **Troubleshooting**

### **If the command fails:**

1. **Check Ganache**: Make sure it's running on `http://127.0.0.1:7545`
2. **Check MetaMask**: Ensure it's connected to Ganache network
3. **Check contract address consistency**:
   ```bash
   # Verify current address
   node set-contract-address.js --show

   # Force update all files
   node set-contract-address.js --update
   ```
4. **Manual retry**:
   ```bash
   cd verifai-smartcontract-solidity
   npx hardhat compile --force
   npm run deploy-and-setup
   ```

### **If frontend still shows errors:**

1. **Clear browser cache**
2. **Verify contract address sync**:
   ```bash
   node set-contract-address.js --show
   ```
3. **Restart React dev server**:
   ```bash
   cd verifai-frontend-react
   npm run vite
   ```

### **Contract Address Mismatch Issues:**

**Common Error**: `could not decode result data (value="0x")`

**Solution**:
1. **Check address consistency**:
   ```bash
   node set-contract-address.js --show
   ```
2. **Update if needed**:
   ```bash
   node set-contract-address.js 0x2948dcd1B5537E3C0a596716b908AE23ab06CDa9
   ```
3. **Verify MetaMask network** matches Ganache (Chain ID: 1337)

## 💡 **Pro Tips**

- **Bookmark this command**: `npm run restart-project`
- **Use centralized address management**: `node set-contract-address.js --show`
- **Quick address updates**: `node set-contract-address.js <new-address>`
- **Force file sync**: `node set-contract-address.js --update`
- **Save time**: No more manual contract deployment
- **Consistent setup**: Same working state every time
- **Test products**: Always have test123 and dffg ready for testing
- **Address validation**: System prevents invalid contract addresses

## 🎉 **Benefits**

- ✅ **No more manual setup** after restarts
- ✅ **Consistent contract addresses** across all files
- ✅ **Pre-registered test products** ready for scanning
- ✅ **Automated verification** ensures everything works
- ✅ **One command** fixes everything
- ✅ **Save hours** of manual configuration

---

**🚀 Never face restart issues again! Just run `npm run restart-project` and you're ready to go!**
