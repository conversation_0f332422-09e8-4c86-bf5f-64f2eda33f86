const { Client } = require('pg');
require('dotenv').config();

// PostgreSQL client
const client = new Client({
    host: "localhost",
    user: "postgres",
    port: 5432,
    password: process.env.DB_PASSWORD,
    database: "postgres"
});

async function debugUsernameMismatch() {
    try {
        await client.connect();
        console.log('Connected to PostgreSQL database');

        console.log('🔍 Investigating username mismatch issue...\n');

        // Check auth table
        console.log('📋 AUTH TABLE:');
        const authAccounts = await client.query('SELECT username, role, first_login FROM auth ORDER BY username');
        authAccounts.rows.forEach(account => {
            console.log(`  Username: "${account.username}" | Role: ${account.role} | First Login: ${account.first_login}`);
        });

        // Check profile table
        console.log('\n📋 PROFILE TABLE:');
        try {
            const profiles = await client.query('SELECT username, name, role FROM profile ORDER BY username');
            if (profiles.rows.length > 0) {
                profiles.rows.forEach(profile => {
                    console.log(`  Username: "${profile.username}" | Name: "${profile.name}" | Role: ${profile.role}`);
                });
            } else {
                console.log('  No profiles found');
            }
        } catch (error) {
            console.log(`  Profile table error: ${error.message}`);
        }

        // Check for case sensitivity issues
        console.log('\n🔍 CASE SENSITIVITY CHECK:');
        const testUsernames = ['testmanu', 'testaManu', 'TestManu', 'TESTMANU'];
        
        for (const testUsername of testUsernames) {
            const authResult = await client.query('SELECT username FROM auth WHERE username = $1', [testUsername]);
            const authFound = authResult.rows.length > 0;
            
            let profileFound = false;
            try {
                const profileResult = await client.query('SELECT username FROM profile WHERE username = $1', [testUsername]);
                profileFound = profileResult.rows.length > 0;
            } catch (error) {
                // Profile table might not exist
            }
            
            console.log(`  "${testUsername}": Auth=${authFound}, Profile=${profileFound}`);
        }

        // Check for similar usernames
        console.log('\n🔍 SIMILAR USERNAME SEARCH:');
        const similarAuth = await client.query("SELECT username FROM auth WHERE username ILIKE '%manu%'");
        console.log('  Auth table matches:');
        similarAuth.rows.forEach(row => {
            console.log(`    "${row.username}"`);
        });

        try {
            const similarProfile = await client.query("SELECT username, name FROM profile WHERE username ILIKE '%manu%' OR name ILIKE '%manu%'");
            console.log('  Profile table matches:');
            similarProfile.rows.forEach(row => {
                console.log(`    Username: "${row.username}", Name: "${row.name}"`);
            });
        } catch (error) {
            console.log('  Profile table not accessible');
        }

        // Check all tables for any username variations
        console.log('\n📊 ALL TABLES ANALYSIS:');
        
        // Get all table names
        const tablesResult = await client.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
        `);
        
        console.log('  Available tables:');
        for (const table of tablesResult.rows) {
            console.log(`    - ${table.table_name}`);
            
            // Check if table has username column
            try {
                const columnsResult = await client.query(`
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = $1 AND column_name ILIKE '%username%'
                `, [table.table_name]);
                
                if (columnsResult.rows.length > 0) {
                    console.log(`      Has username-like columns: ${columnsResult.rows.map(r => r.column_name).join(', ')}`);
                    
                    // Check for manu-related entries
                    try {
                        const dataResult = await client.query(`SELECT * FROM ${table.table_name} WHERE username ILIKE '%manu%' LIMIT 5`);
                        if (dataResult.rows.length > 0) {
                            console.log(`      Found ${dataResult.rows.length} manu-related entries:`);
                            dataResult.rows.forEach(row => {
                                console.log(`        ${JSON.stringify(row)}`);
                            });
                        }
                    } catch (error) {
                        console.log(`      Error querying ${table.table_name}: ${error.message}`);
                    }
                }
            } catch (error) {
                // Skip if can't check columns
            }
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await client.end();
        console.log('\nDatabase connection closed');
    }
}

// Run the debug
debugUsernameMismatch();
