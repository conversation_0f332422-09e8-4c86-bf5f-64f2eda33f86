import {
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Box,
  Typography,
  Container,
  CssBaseline,
  Avatar,
  Link,
  Grid,
  Paper,
  Divider,
  Chip,
  styled,
  alpha,
  useTheme,
  IconButton,
  InputAdornment
} from '@mui/material';
import useAuth from '../../hooks/useAuth';
import { useRef, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeProvider, keyframes } from '@mui/material/styles';
import createAppTheme from '../../theme';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import SecurityIcon from '@mui/icons-material/Security';
import FingerprintIcon from '@mui/icons-material/Fingerprint';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import {
  FuturisticContainer,
  GlassCard,
  NeonButton,
  NeonTextField,
  GradientText,
  NeonAvatar
} from '../common/FuturisticComponents';
import ParticleField from '../common/ParticleField';
import LoadingSpinner from '../common/LoadingSpinner';

const LOGIN_URL = '/auth';

// Ultra-secure animations - minimal exposure time
const secureIconPulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

// Instant transition - no intermediate states for security
const instantFieldTransition = keyframes`
  0% { opacity: 1; }
  100% { opacity: 1; }
`;

// Enhanced Icon Styling System for Maximum Visibility and Contrast
const getIconContrastColor = (theme, variant = 'default') => {
  const variants = {
    default: theme.palette.mode === 'dark'
      ? alpha(theme.palette.common.white, 0.87)
      : alpha(theme.palette.common.black, 0.87),
    primary: theme.palette.mode === 'dark'
      ? theme.palette.primary.light
      : theme.palette.primary.main,
    secondary: theme.palette.mode === 'dark'
      ? theme.palette.secondary.light
      : theme.palette.secondary.main,
    success: theme.palette.mode === 'dark'
      ? theme.palette.success.light
      : theme.palette.success.main,
    muted: theme.palette.mode === 'dark'
      ? alpha(theme.palette.common.white, 0.6)
      : alpha(theme.palette.common.black, 0.6),
  };
  return variants[variant] || variants.default;
};

const getIconShadow = (theme, intensity = 'medium') => {
  const shadows = {
    light: theme.palette.mode === 'dark'
      ? `0 1px 2px ${alpha(theme.palette.common.black, 0.3)}`
      : `0 1px 1px ${alpha(theme.palette.common.white, 0.8)}`,
    medium: theme.palette.mode === 'dark'
      ? `0 1px 3px ${alpha(theme.palette.common.black, 0.5)}`
      : `0 1px 2px ${alpha(theme.palette.common.white, 0.9)}`,
    strong: theme.palette.mode === 'dark'
      ? `0 2px 4px ${alpha(theme.palette.common.black, 0.7)}`
      : `0 2px 3px ${alpha(theme.palette.common.white, 1)}`,
  };
  return shadows[intensity] || shadows.medium;
};

// Enhanced Icon Component for consistent styling
const EnhancedIcon = styled('span')(({ theme, variant = 'default', size = 'medium' }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: getIconContrastColor(theme, variant),
  filter: `brightness(1.1) contrast(1.1) drop-shadow(${getIconShadow(theme, 'medium')})`,
  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',

  '& .MuiSvgIcon-root': {
    fontSize: size === 'small' ? '1rem' : size === 'large' ? '2rem' : '1.25rem',
    filter: 'inherit',
  },

  '&:hover': {
    color: getIconContrastColor(theme, variant === 'default' ? 'primary' : variant),
    filter: `brightness(1.2) contrast(1.2) drop-shadow(${getIconShadow(theme, 'strong')})`,
    transform: 'scale(1.05)',
  },
}));

// Ultra-Secure Eye Icon Button with Dynamic Color Adaptation for Maximum Visibility
const AnimatedEyeButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'isVisible',
})(({ theme, isVisible }) => {
  // Dynamic color calculation for optimal contrast
  const getOptimalColor = () => {
    if (isVisible) {
      // Active state: Use primary color with enhanced contrast
      return theme.palette.mode === 'dark'
        ? theme.palette.primary.light
        : theme.palette.primary.main;
    } else {
      // Inactive state: Use high-contrast muted color
      return theme.palette.mode === 'dark'
        ? alpha(theme.palette.common.white, 0.7)
        : alpha(theme.palette.common.black, 0.6);
    }
  };

  const getHoverColor = () => {
    return theme.palette.mode === 'dark'
      ? theme.palette.primary.light
      : theme.palette.primary.dark;
  };

  const getBackgroundColor = (opacity) => {
    return theme.palette.mode === 'dark'
      ? alpha(theme.palette.primary.light, opacity)
      : alpha(theme.palette.primary.main, opacity);
  };

  return {
    color: getOptimalColor(),
    transition: 'all 0.05s ease-out', // Ultra-fast for security
    borderRadius: '12px',
    padding: '8px',
    position: 'relative',
    overflow: 'hidden',
    // Enhanced contrast background for glassmorphism compatibility
    backgroundColor: alpha(theme.palette.background.paper, 0.1),
    backdropFilter: 'blur(8px)',
    border: `1px solid ${alpha(getOptimalColor(), 0.2)}`,

    '&:hover': {
      backgroundColor: getBackgroundColor(0.12),
      color: getHoverColor(),
      borderColor: alpha(getHoverColor(), 0.4),
      // Enhanced shadow for better visibility
      boxShadow: `0 2px 8px ${alpha(getHoverColor(), 0.25)}`,
    },

    '&:active': {
      backgroundColor: getBackgroundColor(0.18),
      color: getHoverColor(),
      borderColor: alpha(getHoverColor(), 0.6),
      // Instant active state - no transition delays
    },

    '&:focus-visible': {
      outline: `2px solid ${getHoverColor()}`,
      outlineOffset: '2px',
      backgroundColor: getBackgroundColor(0.1),
    },

    '&:disabled': {
      color: alpha(theme.palette.text.disabled, 0.5),
      backgroundColor: alpha(theme.palette.background.paper, 0.05),
      borderColor: alpha(theme.palette.text.disabled, 0.1),
    },

    '& .MuiSvgIcon-root': {
      fontSize: '1.3rem',
      // Enhanced contrast filters for better visibility
      filter: isVisible
        ? `brightness(1.2) contrast(1.1) drop-shadow(0 1px 2px ${alpha(theme.palette.common.black, 0.2)})`
        : `brightness(0.9) contrast(1.05) drop-shadow(0 1px 1px ${alpha(theme.palette.common.black, 0.1)})`,
      zIndex: 1,
      position: 'relative',
      // Instant icon change with subtle pulse only on click
      animation: `${secureIconPulse} 0.1s ease-out`,
      // Additional contrast enhancement for glassmorphism backgrounds
      textShadow: theme.palette.mode === 'dark'
        ? `0 1px 2px ${alpha(theme.palette.common.black, 0.5)}`
        : `0 1px 1px ${alpha(theme.palette.common.white, 0.8)}`,
    },
  };
});

// Ultra-Secure Password Field - Instant transitions to prevent exposure
const AnimatedPasswordField = styled(NeonTextField, {
  shouldForwardProp: (prop) => prop !== 'showPassword',
})(({ showPassword }) => ({
  '& .MuiInputBase-input': {
    // NO transitions on input text for maximum security
    letterSpacing: showPassword ? 'normal' : '0.05em', // Minimal difference
    fontFamily: 'inherit',
    fontSize: 'inherit',
    // Instant change - no animation that could expose content
    '&::placeholder': {
      opacity: 0.6, // Static opacity - no transitions
    },
    // Security enhancement: prevent text selection during toggle
    userSelect: showPassword ? 'text' : 'none',
    // Instant focus styles
    '&:focus': {
      outline: 'none', // Handled by Material-UI
    },
  },
  '& .MuiInputAdornment-root': {
    marginLeft: 0,
    '& .MuiIconButton-root': {
      marginRight: '-8px',
    },
  },
  '& .MuiOutlinedInput-root': {
    // Minimal hover effects for security
    '&:hover': {
      '& .MuiInputAdornment-root .MuiIconButton-root': {
        // No transform animations on hover
        backgroundColor: alpha('#1976d2', 0.04),
      },
    },
    // Enhanced focus security
    '&.Mui-focused': {
      '& .MuiInputAdornment-root .MuiIconButton-root': {
        color: '#1976d2',
      },
    },
  },
}));

const SecurityBadge = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  padding: theme.spacing(1, 2),
  background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.cyber?.main || '#14b8a6', 0.1)} 100%)`,
  border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`,
  borderRadius: '12px',
  backdropFilter: 'blur(10px)',
}));

const FeatureChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
  border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  backdropFilter: 'blur(10px)',
  fontWeight: 600,
  '&:hover': {
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`,
    transform: 'translateY(-2px)',
  },
}));

export default function Login() {
  const { login, isLoading } = useAuth();
  const navigate = useNavigate();
  const errRef = useRef(null);
  const theme = useTheme();

  const [user, setUser] = useState('');
  const [pwd, setPwd] = useState('');
  const [errMsg, setErrMsg] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const handleBack = () => {
    navigate('/');
  };

  const handleTogglePasswordVisibility = () => {
    // Prevent rapid toggling for security
    if (isToggling) return;

    setIsToggling(true);
    // Instant state change for maximum security
    setShowPassword(!showPassword);

    // Immediate focus restoration - no delay for security
    const passwordField = document.getElementById('password');
    if (passwordField) {
      passwordField.focus();
      // Ensure cursor stays at the end of the input
      const length = passwordField.value.length;
      passwordField.setSelectionRange(length, length);
    }

    // Reset toggle state immediately
    setIsToggling(false);
  };

  useEffect(() => {
    setErrMsg('');
  }, [user, pwd]);

const handleSubmit = async (e) => {
  e.preventDefault();
  setIsSubmitting(true);
  setErrMsg('');

  try {
    const result = await login(user, pwd, rememberMe);

    if (result.success) {
      // Clear form
      setUser('');
      setPwd('');

      // Check if password change is required (first-time login)
      if (result.requirePasswordChange) {
        // Store username for password change flow
        localStorage.setItem('first_time_user', result.username);
        navigate('/change-password');
        return;
      }

      // Regular login - navigate based on role
      const { role } = result.user;
      navigate(`/${role}`, { replace: true });
    } else {
      setErrMsg(result.message || 'Login failed. Please try again.');
      if (errRef.current) errRef.current.focus();
    }
  } catch (error) {
    console.error('Login error:', error);
    setErrMsg('An unexpected error occurred. Please try again.');
    if (errRef.current) errRef.current.focus();
  } finally {
    setIsSubmitting(false);
  }
};

  return (
    <ThemeProvider theme={createAppTheme('light')}>
      <CssBaseline />
      <FuturisticContainer>
        <ParticleField density="medium" animated={true} />

        <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2 }}>
          {/* Back Button */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'flex-start',
            width: '100%',
            mb: 3
          }}>
            <NeonButton
              variant="secondary"
              onClick={handleBack}
              startIcon={<ArrowBackIcon />}
              sx={{
                background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                color: theme.palette.text.primary,
              }}
            >
              Back to Home
            </NeonButton>
          </Box>

          {/* Main Login Card */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%'
          }}>
            <GlassCard sx={{ maxWidth: 500, width: '100%', p: 5 }}>
              {/* Header Section */}
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                mb: 4
              }}>
                <NeonAvatar sx={{
                  width: 80,
                  height: 80,
                  mb: 3
                }}>
                  <SecurityIcon fontSize="large" />
                </NeonAvatar>

                <GradientText variant="h2" component="h1" sx={{ mb: 1 }}>
                  Verifai
                </GradientText>

                <Typography variant="h6" component="h2" color="text.secondary" sx={{ mb: 2 }}>
                  Blockchain Authentication Portal
                </Typography>

                {/* Security Features */}
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'center', mb: 3 }}>
                  <FeatureChip
                    icon={<FingerprintIcon />}
                    label="Biometric Ready"
                    size="small"
                  />
                  <FeatureChip
                    icon={<SecurityIcon />}
                    label="256-bit Encryption"
                    size="small"
                  />
                  <FeatureChip
                    icon={<VerifiedUserIcon />}
                    label="Blockchain Secured"
                    size="small"
                  />
                </Box>
              </Box>
              
              {/* Error Message */}
              {errMsg && (
                <Box
                  ref={errRef}
                  sx={{
                    width: '100%',
                    background: 'rgba(211, 47, 47, 0.15)',
                    border: '1px solid #d32f2f',
                    color: '#d32f2f',
                    p: 2,
                    borderRadius: '16px',
                    mb: 3,
                    textAlign: 'center',
                    fontWeight: 600,
                    backdropFilter: 'blur(10px)',
                    fontSize: '0.95rem'
                  }}
                >
                  {errMsg}
                </Box>
              )}

              {/* Login Form */}
              <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
                <NeonTextField
                  margin="normal"
                  required
                  fullWidth
                  id="username"
                  label="Username"
                  name="username"
                  autoComplete="username"
                  autoFocus
                  value={user}
                  onChange={(e) => setUser(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 3 }}
                />

                <AnimatedPasswordField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="current-password"
                  value={pwd}
                  onChange={(e) => setPwd(e.target.value)}
                  variant="outlined"
                  showPassword={showPassword}
                  sx={{ mb: 2 }}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <AnimatedEyeButton
                            aria-label={showPassword ? "Hide password" : "Show password"}
                            onClick={handleTogglePasswordVisibility}
                            onMouseDown={(e) => e.preventDefault()}
                            disabled={isToggling}
                            isVisible={showPassword}
                            edge="end"
                            title={showPassword ? "Hide password" : "Show password"}
                          >
                            {/* Instant icon change - no transitions for security */}
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </AnimatedEyeButton>
                        </InputAdornment>
                      ),
                    },
                  }}
                />
                
                {/* Form Options */}
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 4
                }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        value="remember"
                        color="primary"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        sx={{
                          '&.Mui-checked': {
                            color: theme.palette.primary.main,
                          }
                        }}
                      />
                    }
                    label={
                      <Typography variant="body2" fontWeight={500}>
                        Remember me
                      </Typography>
                    }
                  />

            
                </Box>

                {/* Submit Button */}
                <NeonButton
                  type="submit"
                  fullWidth
                  variant="primary"
                  size="large"
                  disabled={isSubmitting || isLoading}
                  sx={{
                    mb: 4,
                    py: 2,
                    fontSize: '1.1rem',
                  }}
                >
                  {isSubmitting || isLoading ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <LoadingSpinner size={20} />
                      Authenticating...
                    </Box>
                  ) : (
                    'Sign In'
                  )}
                </NeonButton>
                
                {/* Divider */}
                <Divider sx={{
                  my: 4,
                  '&::before, &::after': {
                    borderColor: alpha(theme.palette.primary.main, 0.3),
                  }
                }}>
                  <Typography variant="body2" color="text.secondary" fontWeight={600}>
                    OR
                  </Typography>
                </Divider>

                {/* Additional Options */}
                <Box sx={{ textAlign: 'center', mt: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    Don't have an account?{' '}
                    <Link
                      href="#"
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        color: theme.palette.primary.main,
                        textDecoration: 'none',
                        '&:hover': {
                          textDecoration: 'underline',
                        }
                      }}
                    >
                      Request access
                    </Link>
                  </Typography>
                </Box>
              </Box>
            </GlassCard>

            {/* Security Badge */}
            <SecurityBadge sx={{ mt: 4 }}>
              <SecurityIcon color="success" />
              <Typography variant="body2" fontWeight={600} color="text.secondary">
                Verifai v2.0 • Enterprise Blockchain Security
              </Typography>
            </SecurityBadge>
          </Box>
        </Container>
      </FuturisticContainer>
    </ThemeProvider>
  );
}